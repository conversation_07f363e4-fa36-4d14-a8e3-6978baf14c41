import { promises as fs, existsSync, statSync, constants, type Stats } from 'fs';
import { join, dirname, relative, resolve, extname } from 'path';
import { tmpdir } from 'os';
import { FileStructure } from '../types';

/**
 * Create a temporary workspace for evaluation
 */
export async function createTempWorkspace(prefix: string = 'eval-workspace'): Promise<string> {
  const tempDir = tmpdir();
  const workspaceName = `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const workspacePath = join(tempDir, workspaceName);
  
  await fs.mkdir(workspacePath, { recursive: true });
  return workspacePath;
}

/**
 * Safely remove temporary workspace
 */
export async function cleanupWorkspace(workspacePath: string): Promise<void> {
  try {
    if (existsSync(workspacePath)) {
      await fs.rm(workspacePath, { recursive: true, force: true });
    }
  } catch (error) {
    console.warn(`Failed to cleanup workspace ${workspacePath}:`, error);
  }
}

/**
 * Copy agent output to workspace (extract files from text)
 */
export async function copyAgentOutput(output: string, workspacePath: string): Promise<string[]> {
  const createdFiles: string[] = [];
  
  // Extract file blocks from agent output
  const fileBlocks = extractFileBlocks(output);
  
  for (const block of fileBlocks) {
    try {
      const filePath = join(workspacePath, block.filename);
      const fileDir = dirname(filePath);

      // Create directory if it doesn't exist
      await fs.mkdir(fileDir, { recursive: true });

      // Write file content
      await fs.writeFile(filePath, block.content, 'utf8');
      createdFiles.push(block.filename);
      
    } catch (error) {
      console.warn(`Failed to create file ${block.filename}:`, error);
    }
  }
  
  return createdFiles;
}

/**
 * Extract file blocks from agent output text
 */
function extractFileBlocks(text: string): Array<{ filename: string; content: string }> {
  const blocks: Array<{ filename: string; content: string }> = [];
  
  // Pattern for file blocks with filename
  const fileBlockRegex = /(?:```(?:typescript|javascript|tsx|jsx|ts|js)?\s*(?:\/\/\s*)?(.+?\.(ts|tsx|js|jsx|json|css|html))\s*\n([\s\S]*?)\n```)/gi;
  
  let match;
  while ((match = fileBlockRegex.exec(text)) !== null) {
    const filename = match[1].trim();
    const content = match[3];
    
    if (filename && content) {
      blocks.push({ filename, content });
    }
  }
  
  // Alternative pattern for explicit file creation
  const createFileRegex = /(?:create|write|save)\s+(?:file\s+)?["`']?([^"`'\s]+\.(ts|tsx|js|jsx|json|css|html))["`']?\s*:?\s*\n```[\w]*\n([\s\S]*?)\n```/gi;
  
  while ((match = createFileRegex.exec(text)) !== null) {
    const filename = match[1].trim();
    const content = match[3];
    
    if (filename && content && !blocks.some(b => b.filename === filename)) {
      blocks.push({ filename, content });
    }
  }
  
  return blocks;
}

/**
 * Validate file structure against requirements
 */
export async function validateFileStructure(workspacePath: string, expected: FileStructure): Promise<any> {
  const result = {
    requiredFiles: { found: [], missing: [] },
    optionalFiles: { found: [], missing: [] },
    requiredDirectories: { found: [], missing: [] },
    score: 0
  };
  
  // Check required files
  for (const file of expected.requiredFiles) {
    const filePath = join(workspacePath, file);
    if (existsSync(filePath)) {
      result.requiredFiles.found.push(file);
    } else {
      result.requiredFiles.missing.push(file);
    }
  }

  // Check optional files
  for (const file of expected.optionalFiles || []) {
    const filePath = join(workspacePath, file);
    if (existsSync(filePath)) {
      result.optionalFiles.found.push(file);
    } else {
      result.optionalFiles.missing.push(file);
    }
  }

  // Check required directories
  for (const dir of expected.requiredDirectories || []) {
    const dirPath = join(workspacePath, dir);
    if (existsSync(dirPath) && statSync(dirPath).isDirectory()) {
      result.requiredDirectories.found.push(dir);
    } else {
      result.requiredDirectories.missing.push(dir);
    }
  }
  
  // Calculate score
  const totalRequired = expected.requiredFiles.length + (expected.requiredDirectories?.length || 0);
  const foundRequired = result.requiredFiles.found.length + result.requiredDirectories.found.length;
  result.score = totalRequired > 0 ? foundRequired / totalRequired : 1.0;
  
  return result;
}

/**
 * Find generated files in workspace
 */
export async function findGeneratedFiles(workspacePath: string, extensions: string[]): Promise<string[]> {
  const files: string[] = [];
  
  try {
    await walkDirectory(workspacePath, (filePath) => {
      const relativePath = relative(workspacePath, filePath);
      const ext = extname(filePath);
      
      if (extensions.length === 0 || extensions.includes(ext)) {
        files.push(relativePath);
      }
    });
  } catch (error) {
    console.warn(`Error finding files in ${workspacePath}:`, error);
  }
  
  return files;
}

/**
 * Walk directory recursively
 */
async function walkDirectory(dirPath: string, callback: (filePath: string) => void): Promise<void> {
  const entries = await fs.readdir(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = join(dirPath, entry.name);

    if (entry.isDirectory()) {
      await walkDirectory(fullPath, callback);
    } else if (entry.isFile()) {
      callback(fullPath);
    }
  }
}

/**
 * Read file content with error handling
 */
export async function readFileContent(filePath: string, encoding: BufferEncoding = 'utf8'): Promise<string> {
  try {
    return await fs.readFile(filePath, encoding);
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Write test file to workspace
 */
export async function writeTestFile(filePath: string, content: string): Promise<void> {
  try {
    const dir = dirname(filePath);
    await fs.mkdir(dir, { recursive: true });
    await fs.writeFile(filePath, content, 'utf8');
  } catch (error) {
    throw new Error(`Failed to write test file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Resolve workspace path
 */
export function resolveWorkspacePath(workspacePath: string, relativePath: string): string {
  return resolve(workspacePath, relativePath);
}

/**
 * Check if path is safe (within workspace)
 */
export function isSafePath(workspacePath: string, targetPath: string): boolean {
  const resolvedWorkspace = resolve(workspacePath);
  const resolvedTarget = resolve(targetPath);
  
  return resolvedTarget.startsWith(resolvedWorkspace);
}

/**
 * Get file statistics
 */
export async function getFileStats(filePath: string): Promise<Stats | null> {
  try {
    return await fs.stat(filePath);
  } catch (error) {
    return null;
  }
}

/**
 * Check if file exists and is readable
 */
export async function isFileReadable(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath, constants.R_OK);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Create directory structure
 */
export async function createDirectoryStructure(basePath: string, structure: string[]): Promise<void> {
  for (const dir of structure) {
    const dirPath = join(basePath, dir);
    await fs.mkdir(dirPath, { recursive: true });
  }
}

/**
 * Copy file with error handling
 */
export async function copyFile(sourcePath: string, targetPath: string): Promise<void> {
  try {
    const targetDir = dirname(targetPath);
    await fs.mkdir(targetDir, { recursive: true });
    await fs.copyFile(sourcePath, targetPath);
  } catch (error) {
    throw new Error(`Failed to copy file from ${sourcePath} to ${targetPath}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get directory size in bytes
 */
export async function getDirectorySize(dirPath: string): Promise<number> {
  let totalSize = 0;
  
  try {
    await walkDirectory(dirPath, async (filePath) => {
      const stats = await getFileStats(filePath);
      if (stats) {
        totalSize += stats.size;
      }
    });
  } catch (error) {
    console.warn(`Error calculating directory size for ${dirPath}:`, error);
  }
  
  return totalSize;
}

/**
 * List files matching pattern
 */
export async function listFiles(dirPath: string, pattern?: RegExp): Promise<string[]> {
  const files: string[] = [];
  
  try {
    await walkDirectory(dirPath, (filePath) => {
      const relativePath = relative(dirPath, filePath);
      
      if (!pattern || pattern.test(relativePath)) {
        files.push(relativePath);
      }
    });
  } catch (error) {
    console.warn(`Error listing files in ${dirPath}:`, error);
  }
  
  return files;
}
