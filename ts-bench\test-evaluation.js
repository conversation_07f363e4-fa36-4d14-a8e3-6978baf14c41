#!/usr/bin/env bun

// Simple test to verify the evaluation system works
// This uses JavaScript to avoid TypeScript compilation issues

import { createTempWorkspace, copyAgentOutput } from './src/evaluation/utils/file-utils.js';
import { PatternEvaluator } from './src/evaluation/evaluators/pattern-evaluator.js';
import { EvaluationStrategy, MatchMode } from './src/evaluation/types.js';

async function testEvaluationSystem() {
  console.log('🧪 Testing Evaluation System...');
  
  try {
    // Test 1: Pattern Evaluator
    console.log('\n1. Testing Pattern Evaluator...');
    
    const patternEvaluator = new PatternEvaluator({
      mode: MatchMode.CASE_INSENSITIVE,
      timeout: 5000
    });
    
    const testCase = {
      id: 'test-1',
      name: 'Simple Test',
      description: 'Test pattern matching',
      expectedAnswers: ['hello world', 'success'],
      negativeAnswers: ['error', 'failed']
    };
    
    const agentOutput = 'Hello World! This is a successful test.';
    
    const context = {
      testCase,
      agentOutput,
      workspacePath: '/tmp/test-workspace'
    };
    
    const result = await patternEvaluator.evaluate(context);
    
    console.log('Pattern Evaluation Result:', {
      success: result.success,
      score: result.score,
      strategy: result.strategy,
      details: result.details
    });
    
    // Test 2: File Utils
    console.log('\n2. Testing File Utils...');
    
    const workspace = await createTempWorkspace('test-eval');
    console.log('Created workspace:', workspace);
    
    const agentOutputWithCode = `
Here's the solution:

\`\`\`typescript
// hello.ts
export function greet(name: string): string {
  return \`Hello, \${name}!\`;
}
\`\`\`

\`\`\`json
// package.json
{
  "name": "test-project",
  "version": "1.0.0"
}
\`\`\`
    `;
    
    const createdFiles = await copyAgentOutput(agentOutputWithCode, workspace);
    console.log('Created files:', createdFiles);
    
    console.log('\n✅ Evaluation system test completed successfully!');
    
  } catch (error) {
    console.error('❌ Evaluation system test failed:', error);
    process.exit(1);
  }
}

// Run the test
testEvaluationSystem();
