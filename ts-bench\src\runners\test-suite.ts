import { TestSuiteAgentRunner } from '../agents/test-suite-agent-runner';
import { TestSuiteTestRunner } from '../test-runners/test-suite-test-runner';
import { TestSuiteReader } from '../test-suites/test-suite-reader';
import { BenchmarkConfig } from '../config/types';
import { TestSuiteResult, TestSuiteExecutionResult } from '../config/test-suite-types';
import { Logger } from '../utils/logger';

/**
 * TestSuiteRunner orchestrates the execution of test suite cases
 * Similar to ExerciseRunner but for prompt-based test cases
 */
export class TestSuiteRunner {
    private agentRunner: TestSuiteAgentRunner;
    private testRunner: TestSuiteTestRunner;
    private testSuiteReader: TestSuiteReader;
    private logger: Logger;

    constructor(
        agentRunner: TestSuiteAgentRunner,
        testRunner: TestSuiteTestRunner,
        testSuiteReader: TestSuiteReader,
        logger: Logger
    ) {
        this.agentRunner = agentRunner;
        this.testRunner = testRunner;
        this.testSuiteReader = testSuiteReader;
        this.logger = logger;
    }

    /**
     * Run a single test case from a test suite
     */
    async run(
        config: BenchmarkConfig,
        suiteId: string,
        testCaseId: string
    ): Promise<TestSuiteResult> {
        const startTime = Date.now();
        
        try {
            // Load test suite and validate test case exists
            const testSuite = await this.testSuiteReader.readTestSuite(suiteId);
            if (!testSuite) {
                throw new Error(`Test suite '${suiteId}' not found`);
            }

            const testCase = testSuite.testCases.find(tc => tc.id === testCaseId);
            if (!testCase) {
                throw new Error(`Test case '${testCaseId}' not found in suite '${suiteId}'`);
            }

            this.logger.info(`Running test case ${suiteId}:${testCaseId}`);

            // Execute agent
            const agentStartTime = Date.now();
            const agentResult = await this.agentRunner.run(config, suiteId, testCaseId);
            const agentDuration = Date.now() - agentStartTime;

            if (!agentResult.success) {
                return {
                    suiteId,
                    testCaseId,
                    prompt: testCase.prompt,
                    model: config.model,
                    agent: config.agent,
                    provider: config.provider,
                    success: false,
                    correct: false,
                    error: agentResult.error,
                    duration: agentDuration,
                    agentOutput: agentResult.output,
                    expectedOutput: testCase.expectedAnswer,
                    workspacePath: agentResult.workspacePath,
                    timestamp: new Date()
                };
            }

            // Execute evaluation
            const evalStartTime = Date.now();
            const evaluationResult = await this.testRunner.run(
                config,
                suiteId,
                testCaseId,
                agentResult.output || '',
                agentResult.workspacePath
            );
            const evalDuration = Date.now() - evalStartTime;

            const totalDuration = Date.now() - startTime;

            // Estimate cost (basic implementation)
            const cost = this.estimateCost(config.model, config.provider, testCase.prompt, agentResult.output || '');

            return {
                suiteId,
                testCaseId,
                prompt: testCase.prompt,
                model: config.model,
                agent: config.agent,
                provider: config.provider,
                success: agentResult.success && evaluationResult.success,
                correct: evaluationResult.correct || false,
                error: evaluationResult.error,
                duration: totalDuration,
                cost,
                agentOutput: agentResult.output,
                expectedOutput: testCase.expectedAnswer,
                evaluationDetails: evaluationResult.details,
                workspacePath: agentResult.workspacePath,
                timestamp: new Date()
            };

        } catch (error) {
            const totalDuration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            this.logger.error(`Error running test case ${suiteId}:${testCaseId}: ${errorMessage}`);

            return {
                suiteId,
                testCaseId,
                prompt: '', // Will be filled if test case was loaded
                model: config.model,
                agent: config.agent,
                provider: config.provider,
                success: false,
                correct: false,
                error: errorMessage,
                duration: totalDuration,
                timestamp: new Date()
            };
        }
    }

    /**
     * Run multiple test cases and return detailed execution results
     */
    async runMultiple(
        config: BenchmarkConfig,
        testCases: Array<{ suiteId: string; testCaseId: string }>
    ): Promise<TestSuiteResult[]> {
        const results: TestSuiteResult[] = [];
        
        for (const testCase of testCases) {
            const result = await this.run(config, testCase.suiteId, testCase.testCaseId);
            results.push(result);
        }
        
        return results;
    }

    /**
     * Basic cost estimation - should be enhanced with actual pricing data
     */
    private estimateCost(model: string, provider: string, prompt: string, response: string): number {
        // Basic token estimation (rough approximation)
        const inputTokens = Math.ceil(prompt.length / 4);
        const outputTokens = Math.ceil(response.length / 4);
        
        // Basic pricing (should be moved to a proper pricing module)
        const pricing: Record<string, { input: number; output: number }> = {
            'gpt-4': { input: 0.03 / 1000, output: 0.06 / 1000 },
            'gpt-3.5-turbo': { input: 0.001 / 1000, output: 0.002 / 1000 },
            'claude-3-opus': { input: 0.015 / 1000, output: 0.075 / 1000 },
            'claude-3-sonnet': { input: 0.003 / 1000, output: 0.015 / 1000 },
            'claude-3-haiku': { input: 0.00025 / 1000, output: 0.00125 / 1000 }
        };
        
        const modelPricing = pricing[model] || { input: 0.001 / 1000, output: 0.002 / 1000 };
        return (inputTokens * modelPricing.input) + (outputTokens * modelPricing.output);
    }

    /**
     * Clean up workspace after test execution
     */
    async cleanup(workspacePath?: string): Promise<void> {
        if (workspacePath && this.agentRunner.cleanup) {
            await this.agentRunner.cleanup(workspacePath);
        }
    }
}
