import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import type { BenchmarkConfig, AgentResult } from '../config/types';
import type { CommandExecutor } from '../utils/shell';
import type { Logger } from '../utils/logger';
import type { TestSuiteReader } from '../test-suites/reader';
import { AgentFactory } from '../agents/factory';
import { AgentLoggerFactory } from '../utils/agent-logger';
import { DockerExecutionStrategy } from '../execution/docker-strategy';
import { LocalExecutionStrategy } from '../execution/local-strategy';

export class TestSuiteAgentRunner {
    private workspaceMap: Map<string, string> = new Map();

    constructor(
        private executor: CommandExecutor,
        private testSuiteReader: TestSuiteReader,
        private logger: Logger,
        private containerName: string
    ) {}

    async run(
        config: BenchmarkConfig,
        suiteId: string,
        testCaseId: string,
        useDocker: boolean = true
    ): Promise<AgentResult> {
        const startTime = Date.now();
        
        try {
            // Load the test suite and find the specific test case
            this.logger.info(`Loading test suite: ${suiteId}`);
            const suite = await this.testSuiteReader.loadSuite(suiteId);
            
            const testCase = suite.tests.find(tc => tc.id === testCaseId);
            if (!testCase) {
                throw new Error(`Test case ${testCaseId} not found in suite ${suiteId}`);
            }

            // Map suite:testCase combination to exercise name for logging compatibility
            const exerciseName = `${suiteId}:${testCaseId}`;
            this.logger.info(`Running agent for test case: ${exerciseName}`);

            // Create agent builder for test suite execution
            const agentBuilder = AgentFactory.createForTestSuite(config, this.containerName);
            
            // Build command with test case prompt and system prompt
            const command = await agentBuilder.buildTestSuiteCommand(
                testCase.prompt,
                suite.system_prompt || ''
            );

            // Create temporary working directory for agent execution
            const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'test-suite-'));

            // Store workspace path for later evaluation
            this.workspaceMap.set(exerciseName, tempDir);

            // Create a placeholder file to prevent CLI errors on empty directories
            const placeholderPath = path.join(tempDir, 'README.md');
            await fs.writeFile(placeholderPath, '# Test Suite Workspace\n\nThis is a temporary workspace for test suite execution.\n');

            try {
                // Choose execution strategy
                const strategy = useDocker
                    ? new DockerExecutionStrategy(this.containerName)
                    : new LocalExecutionStrategy();

                // Prepare and execute command
                const prepared = strategy.prepare(command, { exercisePath: tempDir, testFiles: [] });

                if (config.verbose) {
                    this.logger.logAgentCommand(prepared.command);
                }

                this.logger.info(`Executing agent command for ${exerciseName}`);

                const execOptions = { ...prepared.options, timeout: config.timeout };
                const result = await this.executor.execute(prepared.command, execOptions);
                const endTime = Date.now();

                // Collect logs using AgentLoggerFactory to mirror AgentRunner behavior
                const logCollector = AgentLoggerFactory.create(config.agent);
                await logCollector.collect(config, exerciseName, tempDir, result);

                return {
                    exercise: exerciseName,
                    success: result.exitCode === 0,
                    output: result.stdout,
                    error: result.stderr,
                    duration: endTime - startTime,
                    workspacePath: tempDir // Include workspace path for evaluation
                };

            } finally {
                // Note: Don't clean up immediately - workspace is needed for evaluation
                // Cleanup will be handled by the cleanup method after evaluation
            }

        } catch (error) {
            const endTime = Date.now();
            const exerciseName = `${suiteId}:${testCaseId}`;
            
            this.logger.error(`Agent execution failed for ${exerciseName}: ${error}`);
            
            return {
                exercise: exerciseName,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                duration: endTime - startTime
            };
        }
    }

    /**
     * Get workspace path for a given exercise
     */
    getWorkspacePath(exerciseName: string): string | undefined {
        return this.workspaceMap.get(exerciseName);
    }

    /**
     * Clean up workspace for a given exercise
     */
    async cleanupWorkspace(exerciseName: string): Promise<void> {
        const workspacePath = this.workspaceMap.get(exerciseName);
        if (workspacePath) {
            try {
                await fs.rm(workspacePath, { recursive: true, force: true });
                this.workspaceMap.delete(exerciseName);
                this.logger.debug(`Cleaned up workspace for ${exerciseName}: ${workspacePath}`);
            } catch (error) {
                this.logger.warn(`Failed to clean up workspace for ${exerciseName}: ${error}`);
            }
        }
    }

    /**
     * Clean up all workspaces
     */
    async cleanupAllWorkspaces(): Promise<void> {
        const cleanupPromises = Array.from(this.workspaceMap.keys()).map(exerciseName =>
            this.cleanupWorkspace(exerciseName)
        );
        await Promise.all(cleanupPromises);
    }
}
