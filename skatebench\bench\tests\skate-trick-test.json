{"name": "Technical Trick Terminology", "description": "Checks what tricks different AI models can correctly name based on a description.", "system_prompt": "You are a skateboard trick naming assistant. You are given a description of a trick and you need to give the name of the trick. If the trick has multiple names, you should give the common name that most skateboarders would use. Keep answers concise and to the point - don't include names of other tricks.", "tests": [{"prompt": "<PERSON> spins 360 degrees backside and flips in the kickflip direction. The skater does not spin.", "answers": ["tre flip", "360 flip"], "negative_answers": ["backside 360 kickflip", "backside 360 flip", "360 heelflip"]}, {"prompt": "<PERSON> spins 360 degrees frontside and flips in the heelflip direction. The skater does not spin.", "answers": ["laser flip"]}, {"prompt": "Board spins 180 degrees frontside and flips in the kickflip direction. The skater does not spin.", "answers": ["hardflip"], "negative_answers": ["frontside flip", "hard luck flip"]}, {"prompt": "Board spins 180 degrees backside and flips in the kickflip direction. The skater does not spin.", "answers": ["varial"], "negative_answers": ["backside 180 kickflip", "backside flip"]}, {"prompt": "Board spins 180 degrees frontside and flips in the heelflip direction. The skater does not spin.", "answers": ["varial heel"], "negative_answers": ["frontside 180 heelflip", "laser flip"]}, {"prompt": "<PERSON> spins 180 degrees backside and flips in the heelflip direction. The skater does not spin.", "answers": ["inward heel"], "negative_answers": ["varial heel", "backside heel"]}, {"prompt": "<PERSON> spins 360 degrees backside and flips in the kickflip direction. The skater spins 180 degrees", "answers": ["big flip", "bigflip"]}]}