{"lastUpdated": "2025-09-05T15:37:23.629Z", "results": {"codex-gpt-5": {"metadata": {"agent": "codex", "model": "gpt-5", "provider": "openai", "version": "unknown", "timestamp": "2025-08-30T12:30:00.000Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17344734992", "runId": "17344734992", "artifactName": "results-codex-gpt-5"}, "summary": {"successRate": 88, "totalDuration": 2292500, "avgDuration": 91700, "successCount": 22, "totalCount": 25, "agentSuccessCount": 22, "testSuccessCount": 22, "testFailedCount": 3}, "results": []}, "claude-claude-sonnet-4-20250514": {"metadata": {"agent": "claude", "model": "claude-sonnet-4-20250514", "provider": "anthropic", "version": "unknown", "timestamp": "2025-08-30T12:00:00.000Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17344732069", "runId": "17344732069", "artifactName": "results-claude-claude-sonnet-4-20250514"}, "summary": {"successRate": 72, "totalDuration": 5152500, "avgDuration": 206100, "successCount": 18, "totalCount": 25, "agentSuccessCount": 18, "testSuccessCount": 18, "testFailedCount": 7}, "results": []}, "gemini-gemini-2.5-pro": {"metadata": {"agent": "gemini", "model": "gemini-2.5-pro", "provider": "google", "version": "0.2.2", "timestamp": "2025-08-31T02:56:56.692Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17351052819", "runId": "17351052819"}, "summary": {"successRate": 92, "totalDuration": 4213652, "avgDuration": 168546.1, "successCount": 23, "totalCount": 25, "agentSuccessCount": 25, "testSuccessCount": 23, "testFailedCount": 2}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 105484, "testDuration": 7067, "totalDuration": 112696}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 430029, "testDuration": 7305, "totalDuration": 437345}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 420153, "testDuration": 7272, "totalDuration": 427435}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 68181, "testDuration": 7331, "totalDuration": 75523}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 57548, "testDuration": 7330, "totalDuration": 64888}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[31m\u001b[1mUsage Error\u001b[22m\u001b[39m: The nearest package directory (\u001b[38;5;170m/home/<USER>/work/ts-bench/ts-bench/exercism-typescript/exercises/practice/bowling\u001b[39m) doesn't seem to be part of the project declared in \u001b[38;5;170m/home/<USER>/work/ts-bench/ts-bench/exercism-typescript\u001b[39m.\n\n- If \u001b[38;5;170m/home/<USER>/work/ts-bench/ts-bench/exercism-typescript\u001b[39m isn't intended to be a project, remove any yarn.lock and/or package.json file there.\n- If \u001b[38;5;170m/home/<USER>/work/ts-bench/ts-bench/exercism-typescript\u001b[39m is intended to be a project, it might be that you forgot to list \u001b[38;5;170mexercises/practice/bowling\u001b[39m in its workspace configuration.\n- Finally, if \u001b[38;5;170m/home/<USER>/work/ts-bench/ts-bench/exercism-typescript\u001b[39m is fine and you intend \u001b[38;5;170mexercises/practice/bowling\u001b[39m to be treated as a completely separate project (not even a workspace), create an empty yarn.lock file in it.\n\n\u001b[1m$ \u001b[22myarn install [--json] [--immutable] [--immutable-cache] [--refresh-lockfile] [--check-cache] [--check-resolutions] [--inline-builds] [--mode #0]\n\nSTDERR: ", "agentDuration": 305416, "testDuration": 291, "totalDuration": 305718}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 93486, "testDuration": 7370, "totalDuration": 100868}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 76306, "testDuration": 7259, "totalDuration": 83575}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 78528, "testDuration": 7308, "totalDuration": 85847}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65474, "testDuration": 7328, "totalDuration": 72812}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 55590, "testDuration": 7377, "totalDuration": 62979}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65273, "testDuration": 7538, "totalDuration": 72821}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 94883, "testDuration": 7306, "totalDuration": 102200}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 73672, "testDuration": 7431, "totalDuration": 81114}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 495761, "testDuration": 7329, "totalDuration": 503101}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 73228, "testDuration": 7438, "totalDuration": 80676}, {"exercise": "react", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 156451, "testDuration": 7470, "totalDuration": 163932}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 77776, "testDuration": 7374, "totalDuration": 85161}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 449392, "testDuration": 7455, "totalDuration": 456858}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m YN0087: Migrated your project to the latest Yarn version 🚀\n\n\u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n\u001b[94m➤\u001b[39m YN0085: │ \u001b[38;5;70m+\u001b[39m \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mbabel-preset-typescript\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:0.6.0\u001b[39m, \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173meslint-config-typescript\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:0.8.0\u001b[39m, \u001b[38;5;166m@jest/\u001b[39m\u001b[38;5;173mglobals\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:29.7.0\u001b[39m, \u001b[38;5;166m@types/\u001b[39m\u001b[38;5;173mnode\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:22.7.9\u001b[39m, and \u001b[38;5;220m625\u001b[39m more.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-robot-name\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp2c5cf\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n\u001b[91m➤\u001b[39m YN0028: │ The lockfile would have been modified by this install, which is explicitly forbidden.\n::endgroup::\n\u001b[91m➤\u001b[39m YN0028: The lockfile would have been modified by this install, which is explicitly forbidden.\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[91m➤\u001b[39m \nSTDERR: ", "agentDuration": 263451, "testDuration": 4387, "totalDuration": 267849}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 130357, "testDuration": 7444, "totalDuration": 137811}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 104667, "testDuration": 7532, "totalDuration": 112210}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 96109, "testDuration": 9400, "totalDuration": 105520}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 113190, "testDuration": 7548, "totalDuration": 120749}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 86478, "testDuration": 7474, "totalDuration": 93964}]}, "opencode-opencode/grok-code": {"metadata": {"agent": "opencode", "model": "opencode/grok-code", "provider": "xai", "version": "0.5.29", "timestamp": "2025-08-31T09:53:32.454Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17355083421", "runId": "17355083421", "artifactName": "benchmark-results"}, "summary": {"successRate": 88, "totalDuration": 2425115, "avgDuration": 97004.6, "successCount": 22, "totalCount": 25, "agentSuccessCount": 25, "testSuccessCount": 22, "testFailedCount": 3}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 54742, "testDuration": 7202, "totalDuration": 62048}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 30520, "testDuration": 7515, "totalDuration": 38046}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 27975, "testDuration": 7272, "totalDuration": 35259}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 19444, "testDuration": 7257, "totalDuration": 26713}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 46681, "testDuration": 7250, "totalDuration": 53942}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 62759, "testDuration": 7435, "totalDuration": 70205}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 162070, "testDuration": 7302, "totalDuration": 169384}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 62975, "testDuration": 7348, "totalDuration": 70335}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 41156, "testDuration": 7260, "totalDuration": 48428}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 37107, "testDuration": 7282, "totalDuration": 44399}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 42428, "testDuration": 7329, "totalDuration": 49768}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 62431, "testDuration": 7295, "totalDuration": 69737}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 43435, "testDuration": 7215, "totalDuration": 50663}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 67093, "testDuration": 7496, "totalDuration": 74600}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34607, "testDuration": 7759, "totalDuration": 42377}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34427, "testDuration": 7631, "totalDuration": 42070}, {"exercise": "react", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 384392, "testDuration": 7842, "totalDuration": 392246}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 55264, "testDuration": 7506, "totalDuration": 62785}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-relative-distance\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp51e15\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 187932, "testDuration": 7538, "totalDuration": 195483}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 108178, "testDuration": 24281, "totalDuration": 132471}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 51161, "testDuration": 7369, "totalDuration": 58541}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 262265, "testDuration": 7525, "totalDuration": 269802}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 58709, "testDuration": 7514, "totalDuration": 66235}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-variable-length-quantity\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp877a6\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nvariable-length-quantity.test.ts(7,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(11,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(15,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(19,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(23,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(27,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(31,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(35,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(39,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(43,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(47,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(51,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(55,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(59,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(63,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(67,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(71,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(82,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(88,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(92,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(96,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(100,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(104,21): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(109,16): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(115,16): error TS2554: Expected 0 arguments, but got 1.\nvariable-length-quantity.test.ts(125,21): error TS2554: Expected 0 arguments, but got 1.\n\nSTDERR: ", "agentDuration": 116138, "testDuration": 6428, "totalDuration": 122577}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 169502, "testDuration": 7488, "totalDuration": 177001}]}, "qwen-qwen3-coder-plus": {"metadata": {"agent": "qwen", "model": "qwen3-coder-plus", "provider": "dashscope", "version": "0.0.9", "timestamp": "2025-08-31T11:57:22.309Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17356246268", "runId": "17356246268", "artifactName": "benchmark-results"}, "summary": {"successRate": 64, "totalDuration": 3097563, "avgDuration": 123902.5, "successCount": 16, "totalCount": 25, "agentSuccessCount": 16, "testSuccessCount": 16, "testFailedCount": 9}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 85651, "testDuration": 7209, "totalDuration": 93013}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 71163, "testDuration": 7536, "totalDuration": 78710}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 95433, "testDuration": 7270, "totalDuration": 102715}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 82967, "testDuration": 7225, "totalDuration": 90205}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 85907, "testDuration": 7249, "totalDuration": 93168}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 81852, "testDuration": 7286, "totalDuration": 89149}, {"exercise": "complex-numbers", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-complex-numbers\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpe65d4\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 98049, "testDuration": 7530, "totalDuration": 105590}, {"exercise": "connect", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-connect\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8d446\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\ndebug.ts(1,16): error TS1127: Invalid character.\ndebug.ts(1,20): error TS1005: ',' expected.\ndebug.ts(1,32): error TS1127: Invalid character.\ndebug.ts(1,36): error TS1005: ',' expected.\ndebug.ts(1,49): error TS1127: Invalid character.\ndebug.ts(1,53): error TS1005: ',' expected.\ndebug.ts(1,67): error TS1127: Invalid character.\ndebug.ts(1,71): error TS1005: ',' expected.\ndebug.ts(1,86): error TS1127: Invalid character.\ndebug.ts(1,90): error TS1005: ',' expected.\ndebug.ts(1,106): error TS1127: Invalid character.\ndebug.ts(1,110): error TS1127: Invalid character.\ndebug.ts(1,112): error TS1127: Invalid character.\ndebug.ts(1,151): error TS1127: Invalid character.\ndebug.ts(1,180): error TS1127: Invalid character.\ndebug.ts(1,181): error TS1434: Unexpected keyword or identifier.\ndebug.ts(1,216): error TS1127: Invalid character.\ndebug.ts(1,221): error TS1127: Invalid character.\ndebug.ts(1,223): error TS1127: Invalid character.\ndebug.ts(1,257): error TS1127: Invalid character.\ndebug.ts(1,258): error TS1435: Unknown keyword or identifier. Did you mean 'const'?\ndebug.ts(1,292): error TS1127: Invalid character.\ndebug.ts(1,293): error TS1434: Unexpected keyword or identifier.\ndebug.ts(1,325): error TS1127: Invalid character.\ndebug.ts(1,330): error TS1127: Invalid character.\ndebug.ts(1,360): error TS1127: Invalid character.\ndebug.ts(1,361): error TS1434: Unexpected keyword or identifier.\ndebug.ts(1,405): error TS1127: Invalid character.\n\nSTDERR: ", "agentDuration": 300019, "testDuration": 4319, "totalDuration": 304350}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 64870, "testDuration": 7707, "totalDuration": 72588}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 78921, "testDuration": 7712, "totalDuration": 86645}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 67150, "testDuration": 7711, "totalDuration": 74872}, {"exercise": "flatten-array", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-flatten-array\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpec691\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nflatten-array.test.ts(7,20): error TS2554: Expected 0 arguments, but got 1.\nflatten-array.test.ts(12,20): error TS2554: Expected 0 arguments, but got 1.\nflatten-array.test.ts(17,20): error TS2554: Expected 0 arguments, but got 1.\nflatten-array.test.ts(22,20): error TS2554: Expected 0 arguments, but got 1.\nflatten-array.test.ts(28,15): error TS2554: Expected 0 arguments, but got 1.\nflatten-array.test.ts(35,15): error TS2554: Expected 0 arguments, but got 1.\n\nSTDERR: ", "agentDuration": 16980, "testDuration": 6788, "totalDuration": 23780}, {"exercise": "food-chain", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-food-chain\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8fc5f\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nfood-chain.test.ts(10,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(20,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(31,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(43,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(56,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(70,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(85,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(93,18): error TS2554: Expected 0 arguments, but got 1.\nfood-chain.test.ts(106,19): error TS2554: Expected 0 arguments, but got 2.\nfood-chain.test.ts(161,19): error TS2554: Expected 0 arguments, but got 2.\n\nSTDERR: ", "agentDuration": 26098, "testDuration": 6504, "totalDuration": 32614}, {"exercise": "house", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-house\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp71b57\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nhouse.test.ts(7,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(15,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(24,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(34,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(45,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(57,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(70,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(84,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(99,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(115,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(132,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(150,18): error TS2554: Expected 0 arguments, but got 1.\nhouse.test.ts(192,19): error TS2554: Expected 0 arguments, but got 2.\nhouse.test.ts(289,19): error TS2554: Expected 0 arguments, but got 2.\n\nSTDERR: ", "agentDuration": 24696, "testDuration": 6482, "totalDuration": 31190}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 67738, "testDuration": 7404, "totalDuration": 75154}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 83585, "testDuration": 7743, "totalDuration": 91339}, {"exercise": "react", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300020, "testDuration": 8057, "totalDuration": 308089}, {"exercise": "rectangles", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-rectangles\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp58fb9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nrectangles.test.ts(7,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(13,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(19,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(25,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(31,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(37,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(43,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(49,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(55,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(61,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(67,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(79,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(91,26): error TS2554: Expected 0 arguments, but got 1.\nrectangles.test.ts(106,26): error TS2554: Expected 0 arguments, but got 1.\n\nSTDERR: ", "agentDuration": 25494, "testDuration": 6843, "totalDuration": 32348}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 83116, "testDuration": 7682, "totalDuration": 90810}, {"exercise": "robot-name", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-robot-name\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp2c5cf\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300015, "testDuration": 31774, "totalDuration": 331800}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 134281, "testDuration": 7404, "totalDuration": 141696}, {"exercise": "transpose", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-transpose\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp77e24\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300021, "testDuration": 7497, "totalDuration": 307529}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 88690, "testDuration": 7903, "totalDuration": 96605}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 101032, "testDuration": 7588, "totalDuration": 108631}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 227481, "testDuration": 7481, "totalDuration": 234973}]}, "claude-deepseek-reasoner": {"metadata": {"agent": "claude", "model": "deepseek-reasoner", "provider": "deepseek", "version": "1.0.98", "timestamp": "2025-08-31T14:32:49.717Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17357196715", "runId": "17357196715", "artifactName": "benchmark-results"}, "summary": {"successRate": 32, "totalDuration": 7099810, "avgDuration": 283992.4, "successCount": 8, "totalCount": 25, "agentSuccessCount": 8, "testSuccessCount": 11, "testFailedCount": 14}, "results": [{"exercise": "acronym", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-acronym\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpc81cd\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n\u001b[94m➤\u001b[39m YN0013: │ \u001b[38;5;220m270\u001b[39m packages were added to the project (\u001b[38;5;160m+ 15.11 MiB\u001b[39m).\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300438, "testDuration": 11341, "totalDuration": 311931}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 270600, "testDuration": 9073, "totalDuration": 279684}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 295918, "testDuration": 7126, "totalDuration": 303056}, {"exercise": "binary-search", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-binary-search\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpe9bf4\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nbinary-search.ts(15,27): error TS18046: 'needle' is of type 'unknown'.\n\nSTDERR: ", "agentDuration": 300705, "testDuration": 6247, "totalDuration": 306963}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 193460, "testDuration": 7202, "totalDuration": 200673}, {"exercise": "bowling", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bowling\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8a986\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300316, "testDuration": 7391, "totalDuration": 307718}, {"exercise": "complex-numbers", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-complex-numbers\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpe65d4\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300327, "testDuration": 9021, "totalDuration": 309359}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 201221, "testDuration": 7180, "totalDuration": 208411}, {"exercise": "crypto-square", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-crypto-square\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp37190\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n  console.log\n    Test 1: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 2: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 3: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 4: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 5: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 6: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n  console.log\n    Test 7: PASS\n\n      at log (test.js:17:11)\n          at Array.forEach (<anonymous>)\n\n\nSTDERR: ", "agentDuration": 300375, "testDuration": 9971, "totalDuration": 310370}, {"exercise": "diamond", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-diamond\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp56a80\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n  console.log\n    Testing diamond implementation:\n\n      at Object.log (test.js:3:9)\n\n  console.log\n    \n    A:\n\n      at Object.log (test.js:4:9)\n\n  console.log\n    A\n\n      at Object.log (test.js:5:9)\n\n  console.log\n    \n    C:\n\n      at Object.log (test.js:6:9)\n\n  console.log\n      A  \n     B B \n    C   C\n     B B \n      A\n\n      at Object.log (test.js:7:9)\n\n  console.log\n    \n    E:\n\n      at Object.log (test.js:8:9)\n\n  console.log\n        A    \n       B B   \n      C   C  \n     D     D \n    E       E\n     D     D \n      C   C  \n       B B   \n        A\n\n      at Object.log (test.js:9:9)\n\n\nSTDERR: ", "agentDuration": 300504, "testDuration": 8464, "totalDuration": 309001}, {"exercise": "dnd-character", "agentSuccess": false, "testSuccess": true, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "agentDuration": 300359, "testDuration": 9056, "totalDuration": 309425}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 158617, "testDuration": 7110, "totalDuration": 165737}, {"exercise": "food-chain", "agentSuccess": false, "testSuccess": true, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "agentDuration": 300385, "testDuration": 8616, "totalDuration": 309012}, {"exercise": "house", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-house\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp71b57\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n  console.log\n    Testing verse 1:\n\n      at Object.log (test.js:4:9)\n\n  console.log\n    [ 'This is the house that Jack built.' ]\n\n      at Object.log (test.js:5:9)\n\n  console.log\n    undefined\n\n      at Object.log (test.js:6:9)\n\n  console.log\n    Testing verse 2:\n\n      at Object.log (test.js:8:9)\n\n  console.log\n    [ 'This is the malt', 'that lay in the house that Jack built.' ]\n\n      at Object.log (test.js:9:9)\n\n  console.log\n    undefined\n\n      at Object.log (test.js:10:9)\n\n  console.log\n    Testing verse 12:\n\n      at Object.log (test.js:12:9)\n\n  console.log\n    [\n      'This is the horse and the hound and the horn',\n      'that belonged to the farmer sowing his corn',\n      'that kept the rooster that crowed in the morn',\n      'that woke the priest all shaven and shorn',\n      'that married the man all tattered and torn',\n      'that kissed the maiden all forlorn',\n      'that milked the cow with the crumpled horn',\n      'that tossed the dog',\n      'that worried the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.'\n    ]\n\n      at Object.log (test.js:13:9)\n\n  console.log\n    undefined\n\n      at Object.log (test.js:14:9)\n\n  console.log\n    Testing verses 4-8:\n\n      at Object.log (test.js:17:9)\n\n  console.log\n    [\n      'This is the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.',\n      '',\n      'This is the dog',\n      'that worried the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.',\n      '',\n      'This is the cow with the crumpled horn',\n      'that tossed the dog',\n      'that worried the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.',\n      '',\n      'This is the maiden all forlorn',\n      'that milked the cow with the crumpled horn',\n      'that tossed the dog',\n      'that worried the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.',\n      '',\n      'This is the man all tattered and torn',\n      'that kissed the maiden all forlorn',\n      'that milked the cow with the crumpled horn',\n      'that tossed the dog',\n      'that worried the cat',\n      'that killed the rat',\n      'that ate the malt',\n      'that lay in the house that Jack built.'\n    ]\n\n      at Object.log (test.js:18:9)\n\n\nSTDERR: ", "agentDuration": 300429, "testDuration": 9756, "totalDuration": 310195}, {"exercise": "pascals-triangle", "agentSuccess": false, "testSuccess": true, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "agentDuration": 300478, "testDuration": 8062, "totalDuration": 308555}, {"exercise": "rational-numbers", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-rational-numbers\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp16607\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nrational-numbers.test.ts(194,47): error TS2554: Expected 0 arguments, but got 1.\nrational-numbers.test.ts(199,48): error TS2554: Expected 0 arguments, but got 1.\nrational-numbers.test.ts(204,47): error TS2554: Expected 0 arguments, but got 1.\n\nSTDERR: ", "agentDuration": 300361, "testDuration": 6266, "totalDuration": 306638}, {"exercise": "react", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300325, "testDuration": 9308, "totalDuration": 309644}, {"exercise": "rectangles", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-rectangles\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp58fb9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n  console.log\n    Test 1 - Empty array: 0\n\n      at Object.log (test.js:3:9)\n\n  console.log\n    Test 2 - Empty string: 0\n\n      at Object.log (test.js:4:9)\n\n  console.log\n    Test 3 - Space: 0\n\n      at Object.log (test.js:5:9)\n\n  console.log\n    Test 4 - Simple rectangle: 1\n\n      at Object.log (test.js:6:9)\n\n  console.log\n    Test 5 - Two rectangles: 2\n\n      at Object.log (test.js:7:9)\n\n\nSTDERR: ", "agentDuration": 300408, "testDuration": 9731, "totalDuration": 310149}, {"exercise": "relative-distance", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-relative-distance\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp51e15\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n\u001b[94m➤\u001b[39m YN0013: │ \u001b[38;5;220m136\u001b[39m packages were added to the project (\u001b[38;5;160m+ 12.14 MiB\u001b[39m).\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.41.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300516, "testDuration": 11102, "totalDuration": 311629}, {"exercise": "robot-name", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-robot-name\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp2c5cf\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300418, "testDuration": 14859, "totalDuration": 315287}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 138455, "testDuration": 7293, "totalDuration": 145759}, {"exercise": "transpose", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-transpose\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp77e24\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300334, "testDuration": 7425, "totalDuration": 307770}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 252296, "testDuration": 7449, "totalDuration": 259755}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 265935, "testDuration": 7460, "totalDuration": 273405}, {"exercise": "wordy", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-wordy\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp666b9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300420, "testDuration": 9253, "totalDuration": 309684}]}, "opencode-openai/gpt-5": {"metadata": {"agent": "opencode", "model": "openai/gpt-5", "provider": "openai", "version": "0.5.29", "timestamp": "2025-09-01T03:29:58.682Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/yukukotani/ts-bench/actions/runs/17366415419", "runId": "17366415419", "artifactName": "benchmark-results"}, "summary": {"successRate": 96, "totalDuration": 1619762, "avgDuration": 64790.5, "successCount": 24, "totalCount": 25, "agentSuccessCount": 25, "testSuccessCount": 24, "testFailedCount": 1}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 125542, "testDuration": 7173, "totalDuration": 132863}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 31380, "testDuration": 7295, "totalDuration": 38686}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 38131, "testDuration": 7220, "totalDuration": 45364}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 28437, "testDuration": 7222, "totalDuration": 35672}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 32664, "testDuration": 7192, "totalDuration": 39868}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34593, "testDuration": 7504, "totalDuration": 42109}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 78611, "testDuration": 7519, "totalDuration": 86142}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 48998, "testDuration": 7218, "totalDuration": 56228}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 52785, "testDuration": 7302, "totalDuration": 60097}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 36241, "testDuration": 7271, "totalDuration": 43524}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 35278, "testDuration": 7280, "totalDuration": 42570}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 35126, "testDuration": 7304, "totalDuration": 42441}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 33396, "testDuration": 7270, "totalDuration": 40677}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34857, "testDuration": 7330, "totalDuration": 42198}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34146, "testDuration": 7405, "totalDuration": 41562}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 37463, "testDuration": 7551, "totalDuration": 45025}, {"exercise": "react", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 195115, "testDuration": 7551, "totalDuration": 202679}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 43259, "testDuration": 7417, "totalDuration": 50689}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 40379, "testDuration": 7428, "totalDuration": 47817}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 120384, "testDuration": 23653, "totalDuration": 144048}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 27971, "testDuration": 7347, "totalDuration": 35329}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 35277, "testDuration": 7478, "totalDuration": 42766}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 136316, "testDuration": 7494, "totalDuration": 143821}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 29650, "testDuration": 7511, "totalDuration": 37174}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 72895, "testDuration": 7506, "totalDuration": 80413}]}, "aider-claude-sonnet-4-20250514": {"metadata": {"agent": "aider", "model": "claude-sonnet-4-20250514", "provider": "anthropic", "version": "0.86.1", "timestamp": "2025-09-01T08:02:49.916Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17371119174", "runId": "17371119174", "artifactName": "benchmark-results"}, "summary": {"successRate": 32, "totalDuration": 1011910, "avgDuration": 40476.4, "successCount": 8, "totalCount": 25, "agentSuccessCount": 24, "testSuccessCount": 8, "testFailedCount": 17}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-acronym\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpc81cd\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n\u001b[94m➤\u001b[39m YN0013: │ \u001b[38;5;220m633\u001b[39m packages were added to the project (\u001b[38;5;160m+ 32.06 MiB\u001b[39m).\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 10598, "testDuration": 16625, "totalDuration": 27370}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 10812, "testDuration": 9109, "totalDuration": 19932}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bank-account\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp9db5a\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nbank-account.test.ts(122,7): error TS2578: Unused '@ts-expect-error' directive.\n\nSTDERR: ", "agentDuration": 161858, "testDuration": 6344, "totalDuration": 168213}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-binary-search\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpe9bf4\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nbinary-search.ts(11,16): error TS18046: 'midValue' is of type 'unknown'.\nbinary-search.ts(11,27): error TS18046: 'needle' is of type 'unknown'.\n\nSTDERR: ", "agentDuration": 10318, "testDuration": 6325, "totalDuration": 16654}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-binary-search-tree\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpa5dc1\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nbinary-search-tree.ts(23,9): error TS18046: 'item' is of type 'unknown'.\nbinary-search-tree.ts(23,17): error TS2571: Object is of type 'unknown'.\n\nSTDERR: ", "agentDuration": 16183, "testDuration": 6487, "totalDuration": 22681}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bowling\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8a986\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 25463, "testDuration": 9553, "totalDuration": 35027}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-complex-numbers\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpe65d4\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 17146, "testDuration": 9577, "totalDuration": 26734}, {"exercise": "connect", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-connect\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8d446\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 41814, "testDuration": 9270, "totalDuration": 51096}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 12969, "testDuration": 9120, "totalDuration": 22099}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 15542, "testDuration": 9176, "totalDuration": 24728}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-dnd-character\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp1c27b\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\ndnd-character.test.ts(82,22): error TS2339: Property 'hitpoints' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(83,50): error TS2339: Property 'constitution' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(90,22): error TS2339: Property 'strength' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(91,22): error TS2339: Property 'strength' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(97,22): error TS2339: Property 'dexterity' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(98,22): error TS2339: Property 'dexterity' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(104,22): error TS2339: Property 'constitution' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(105,22): error TS2339: Property 'constitution' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(111,22): error TS2339: Property 'intelligence' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(112,22): error TS2339: Property 'intelligence' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(118,22): error TS2339: Property 'wisdom' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(119,22): error TS2339: Property 'wisdom' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(125,22): error TS2339: Property 'charisma' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(126,22): error TS2339: Property 'charisma' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(132,22): error TS2339: Property 'strength' does not exist on type 'DnDCharacter'.\ndnd-character.test.ts(132,45): error TS2339: Property 'strength' does not exist on type 'DnDCharacter'.\n\nSTDERR: ", "agentDuration": 31432, "testDuration": 6466, "totalDuration": 37912}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 9035, "testDuration": 9277, "totalDuration": 18323}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 35205, "testDuration": 9254, "totalDuration": 44469}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 15013, "testDuration": 9112, "totalDuration": 24135}, {"exercise": "pascals-triangle", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-pascals-triangle\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp6c295\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\npascals-triangle.test.ts(6,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(6,28): error TS2339: Property 'rows' does not exist on type 'Triangle'.\npascals-triangle.test.ts(10,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(10,28): error TS2339: Property 'rows' does not exist on type 'Triangle'.\npascals-triangle.test.ts(14,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(14,28): error TS2339: Property 'rows' does not exist on type 'Triangle'.\npascals-triangle.test.ts(18,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(18,28): error TS2339: Property 'lastRow' does not exist on type 'Triangle'.\npascals-triangle.test.ts(22,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(22,28): error TS2339: Property 'lastRow' does not exist on type 'Triangle'.\npascals-triangle.test.ts(30,25): error TS2554: Expected 0 arguments, but got 1.\npascals-triangle.test.ts(30,29): error TS2339: Property 'lastRow' does not exist on type 'Triangle'.\n\nSTDERR: ", "agentDuration": 116292, "testDuration": 6377, "totalDuration": 122681}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 23363, "testDuration": 9493, "totalDuration": 32867}, {"exercise": "react", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 23796, "testDuration": 9685, "totalDuration": 33492}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-rectangles\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp58fb9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 17703, "testDuration": 9330, "totalDuration": 27044}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-relative-distance\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp51e15\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n\u001b[94m➤\u001b[39m YN0013: │ \u001b[38;5;220m147\u001b[39m packages were added to the project (\u001b[38;5;160m+ 12.31 MiB\u001b[39m).\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.41.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 35510, "testDuration": 11378, "totalDuration": 46899}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-robot-name\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp2c5cf\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 12018, "testDuration": 30361, "totalDuration": 42390}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 11547, "testDuration": 9310, "totalDuration": 20868}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-transpose\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp77e24\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 27309, "testDuration": 9446, "totalDuration": 36765}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-two-bucket\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mpf46d6\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 35497, "testDuration": 9474, "totalDuration": 44981}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-variable-length-quantity\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp877a6\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 29808, "testDuration": 9529, "totalDuration": 39347}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-wordy\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp666b9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 15811, "testDuration": 9382, "totalDuration": 25203}]}, "goose-claude-sonnet-4-20250514": {"metadata": {"agent": "goose", "model": "claude-sonnet-4-20250514", "provider": "anthropic", "version": "1.7.0", "timestamp": "2025-09-01T10:06:19.097Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17373186071", "runId": "17373186071", "artifactName": "benchmark-results"}, "summary": {"successRate": 92, "totalDuration": 3054763, "avgDuration": 122190.5, "successCount": 23, "totalCount": 25, "agentSuccessCount": 24, "testSuccessCount": 23, "testFailedCount": 2}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 88529, "testDuration": 7228, "totalDuration": 95904}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 69770, "testDuration": 7404, "totalDuration": 77185}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bank-account\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp9db5a\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\nbank-account.test.ts(122,7): error TS2578: Unused '@ts-expect-error' directive.\n\nSTDERR: ", "agentDuration": 96930, "testDuration": 4484, "totalDuration": 101425}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 55863, "testDuration": 7408, "totalDuration": 63281}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 124425, "testDuration": 7323, "totalDuration": 131758}, {"exercise": "bowling", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 252510, "testDuration": 7466, "totalDuration": 259987}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 210058, "testDuration": 7353, "totalDuration": 217423}, {"exercise": "connect", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-connect\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8d446\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300017, "testDuration": 7443, "totalDuration": 307471}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 61982, "testDuration": 7349, "totalDuration": 69341}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 63663, "testDuration": 7419, "totalDuration": 71092}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 57446, "testDuration": 7349, "totalDuration": 64806}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 70270, "testDuration": 7340, "totalDuration": 77621}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 98239, "testDuration": 7322, "totalDuration": 105571}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 78606, "testDuration": 7400, "totalDuration": 86017}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 57907, "testDuration": 7516, "totalDuration": 65434}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 74453, "testDuration": 7572, "totalDuration": 82035}, {"exercise": "react", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 203737, "testDuration": 7625, "totalDuration": 211374}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65845, "testDuration": 7478, "totalDuration": 73333}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 91965, "testDuration": 7543, "totalDuration": 99519}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 145008, "testDuration": 30155, "totalDuration": 175175}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 66176, "testDuration": 7506, "totalDuration": 73692}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 133988, "testDuration": 7538, "totalDuration": 141537}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 174389, "testDuration": 7565, "totalDuration": 181965}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 100163, "testDuration": 7622, "totalDuration": 107795}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 106401, "testDuration": 7609, "totalDuration": 114022}]}, "opencode-anthropic/claude-sonnet-4-20250514": {"metadata": {"agent": "opencode", "model": "anthropic/claude-sonnet-4-20250514", "provider": "anthropic", "version": "0.5.29", "timestamp": "2025-09-01T11:27:36.489Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17375043809", "runId": "17375043809", "artifactName": "benchmark-results"}, "summary": {"successRate": 92, "totalDuration": 3196227, "avgDuration": 127849.1, "successCount": 23, "totalCount": 25, "agentSuccessCount": 23, "testSuccessCount": 23, "testFailedCount": 2}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 95307, "testDuration": 7184, "totalDuration": 102641}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 69602, "testDuration": 7258, "totalDuration": 76871}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 107022, "testDuration": 7209, "totalDuration": 114242}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65339, "testDuration": 7307, "totalDuration": 72657}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 80266, "testDuration": 7184, "totalDuration": 87460}, {"exercise": "bowling", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bowling\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8a986\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300042, "testDuration": 7509, "totalDuration": 307567}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 238330, "testDuration": 7300, "totalDuration": 245643}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 92428, "testDuration": 7323, "totalDuration": 99762}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65923, "testDuration": 7264, "totalDuration": 73197}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 64453, "testDuration": 7315, "totalDuration": 71779}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 75116, "testDuration": 7393, "totalDuration": 82520}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 80047, "testDuration": 7291, "totalDuration": 87349}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 67468, "testDuration": 7262, "totalDuration": 74741}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 76173, "testDuration": 7371, "totalDuration": 83555}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 65778, "testDuration": 7371, "totalDuration": 73161}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 76660, "testDuration": 7434, "totalDuration": 84106}, {"exercise": "react", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON><PERSON>n explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300039, "testDuration": 8285, "totalDuration": 308343}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 121037, "testDuration": 7402, "totalDuration": 128450}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 88295, "testDuration": 7563, "totalDuration": 95869}, {"exercise": "robot-name", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 131477, "testDuration": 36872, "totalDuration": 168360}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 93805, "testDuration": 7423, "totalDuration": 101239}, {"exercise": "transpose", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 125239, "testDuration": 7513, "totalDuration": 132763}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 208755, "testDuration": 7536, "totalDuration": 216302}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 75700, "testDuration": 7612, "totalDuration": 83324}, {"exercise": "wordy", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 216833, "testDuration": 7480, "totalDuration": 224326}]}, "claude-glm-4.5": {"metadata": {"agent": "claude", "model": "glm-4.5", "provider": "zai", "version": "1.0.107", "timestamp": "2025-09-05T15:22:42.492Z", "exerciseCount": 25, "benchmarkVersion": "1.0.0", "generatedBy": "ts-bench", "runUrl": "https://github.com/laiso/ts-bench/actions/runs/17495591219", "runId": "17495591219", "artifactName": "benchmark-results"}, "summary": {"successRate": 80, "totalDuration": 4308475, "avgDuration": 172339, "successCount": 20, "totalCount": 25, "agentSuccessCount": 21, "testSuccessCount": 21, "testFailedCount": 4}, "results": [{"exercise": "acronym", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 89451, "testDuration": 8070, "totalDuration": 97672}, {"exercise": "anagram", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 94730, "testDuration": 8709, "totalDuration": 103452}, {"exercise": "bank-account", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 132961, "testDuration": 8507, "totalDuration": 141482}, {"exercise": "binary-search", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 78224, "testDuration": 8615, "totalDuration": 86852}, {"exercise": "binary-search-tree", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 100726, "testDuration": 8472, "totalDuration": 109212}, {"exercise": "bowling", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-bowling\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp8a986\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\ntest-runner.ts(49,35): error TS18046: 'error' is of type 'unknown'.\n\nSTDERR: ", "agentDuration": 300435, "testDuration": 6776, "totalDuration": 307223}, {"exercise": "complex-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 205348, "testDuration": 7813, "totalDuration": 213173}, {"exercise": "connect", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 60770, "testDuration": 8248, "totalDuration": 69031}, {"exercise": "crypto-square", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 132647, "testDuration": 9884, "totalDuration": 142543}, {"exercise": "diamond", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 60696, "testDuration": 8150, "totalDuration": 68859}, {"exercise": "dnd-character", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 67946, "testDuration": 10199, "totalDuration": 78158}, {"exercise": "flatten-array", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 130555, "testDuration": 9866, "totalDuration": 140435}, {"exercise": "food-chain", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 201514, "testDuration": 7772, "totalDuration": 209297}, {"exercise": "house", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 156103, "testDuration": 8059, "totalDuration": 164175}, {"exercise": "pascals-triangle", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 96966, "testDuration": 7666, "totalDuration": 104643}, {"exercise": "rational-numbers", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 120617, "testDuration": 7730, "totalDuration": 128359}, {"exercise": "react", "agentSuccess": true, "testSuccess": false, "overallSuccess": false, "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-react\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp31db9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111<PERSON>arn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 245227, "testDuration": 9505, "totalDuration": 254742}, {"exercise": "rectangles", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 141381, "testDuration": 10538, "totalDuration": 151932}, {"exercise": "relative-distance", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 58939, "testDuration": 8841, "totalDuration": 67793}, {"exercise": "robot-name", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-robot-name\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp2c5cf\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111<PERSON>arn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m ::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: Execution timed out after 300 seconds", "agentDuration": 300230, "testDuration": 322590, "totalDuration": 622833}, {"exercise": "spiral-matrix", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 34909, "testDuration": 7751, "totalDuration": 42672}, {"exercise": "transpose", "agentSuccess": false, "testSuccess": true, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "agentDuration": 300159, "testDuration": 7784, "totalDuration": 307955}, {"exercise": "two-bucket", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 135869, "testDuration": 7917, "totalDuration": 143798}, {"exercise": "variable-length-quantity", "agentSuccess": true, "testSuccess": true, "overallSuccess": true, "agentDuration": 234553, "testDuration": 7728, "totalDuration": 242293}, {"exercise": "wordy", "agentSuccess": false, "testSuccess": false, "overallSuccess": false, "agentError": "Execution timed out after 300 seconds", "testError": "STDOUT: \u001b[94m➤\u001b[39m \u001b[94m➤\u001b[39m \u001b[90m::group::Resolution step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Post-resolution validation\n\u001b[93m➤\u001b[39m YN0002: │ \u001b[38;5;166m@exercism/\u001b[39m\u001b[38;5;173mtypescript-wordy\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mworkspace:.\u001b[39m doesn't provide \u001b[38;5;166m@babel/\u001b[39m\u001b[38;5;173mcore\u001b[39m (\u001b[38;5;111mp666b9\u001b[39m), requested by \u001b[38;5;173mbabel-jest\u001b[39m.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by your project; run \u001b[38;5;111myarn explain peer-requirements <hash>\u001b[39m for details, where \u001b[38;5;111m<hash>\u001b[39m is the six-letter p-prefixed code.\n\u001b[93m➤\u001b[39m YN0086: │ Some peer dependencies are incorrectly met by dependencies; run \u001b[38;5;111myarn explain peer-requirements\u001b[39m for details.\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Fetch step\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[94m➤\u001b[39m \u001b[90m::group::Link step\n\u001b[93m➤\u001b[39m \u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.38.1\u001b[39m must be built because it never has been before or the last one failed\n\u001b[94m➤\u001b[39m YN0007: │ \u001b[38;5;173mcore-js\u001b[39m\u001b[38;5;111m@\u001b[39m\u001b[38;5;111mnpm:3.44.0\u001b[39m must be built because it never has been before or the last one failed\n::endgroup::\n\u001b[94m➤\u001b[39m \u001b[90m\u001b[93m➤\u001b[39m [tests] tsc: ✅, tstyche: ❌, jest: ✅, \n[tests] tsc (compile)\n[tests] tstyche (implementation tests)\n\nSTDERR: ", "agentDuration": 300226, "testDuration": 9653, "totalDuration": 309891}]}}}