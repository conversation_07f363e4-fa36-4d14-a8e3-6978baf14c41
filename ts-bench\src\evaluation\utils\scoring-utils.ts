import { EvaluationResult, EvaluationStrategy } from '../types';

/**
 * Calculate overall score from multiple evaluation results
 */
export function calculateScore(results: EvaluationResult[]): number {
  if (results.length === 0) {
    return 0;
  }
  
  const totalScore = results.reduce((sum, result) => sum + result.score, 0);
  return totalScore / results.length;
}

/**
 * Aggregate multiple evaluation results into a single result
 */
export function aggregateResults(results: EvaluationResult[]): EvaluationResult {
  if (results.length === 0) {
    return createFailureResult('No evaluation results to aggregate');
  }
  
  if (results.length === 1) {
    return results[0];
  }
  
  const overallScore = calculateScore(results);
  const successCount = results.filter(r => r.success).length;
  const overallSuccess = successCount > results.length / 2; // Majority success
  
  const aggregatedDetails = {
    individualResults: results,
    successCount,
    failureCount: results.length - successCount,
    averageScore: overallScore,
    strategies: results.map(r => r.strategy)
  };
  
  const errors = results
    .filter(r => r.error)
    .map(r => r.error)
    .filter(Boolean);
  
  return {
    score: overallScore,
    success: overallSuccess,
    details: aggregatedDetails,
    error: errors.length > 0 ? errors.join('; ') : undefined,
    strategy: EvaluationStrategy.COMPOSITE,
    executionTime: results.reduce((sum, r) => sum + (r.executionTime || 0), 0)
  };
}

/**
 * Normalize score to a specific range
 */
export function normalizeScore(score: number, min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('Min value must be less than max value');
  }
  
  return Math.max(min, Math.min(max, score));
}

/**
 * Calculate weighted average of scores
 */
export function weightedAverage(scores: number[], weights: number[]): number {
  if (scores.length !== weights.length) {
    throw new Error('Scores and weights arrays must have the same length');
  }
  
  if (scores.length === 0) {
    return 0;
  }
  
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  if (totalWeight === 0) {
    return 0;
  }
  
  const weightedSum = scores.reduce((sum, score, index) => sum + (score * weights[index]), 0);
  return weightedSum / totalWeight;
}

/**
 * Create a success evaluation result
 */
export function createSuccessResult(
  score: number, 
  strategy: EvaluationStrategy = EvaluationStrategy.PATTERN,
  details?: any
): EvaluationResult {
  return {
    score: normalizeScore(score),
    success: true,
    details,
    strategy,
    executionTime: 0
  };
}

/**
 * Create a failure evaluation result
 */
export function createFailureResult(
  error: string, 
  strategy: EvaluationStrategy = EvaluationStrategy.PATTERN,
  details?: any
): EvaluationResult {
  return {
    score: 0,
    success: false,
    error,
    details,
    strategy,
    executionTime: 0
  };
}

/**
 * Merge evaluation details from multiple results
 */
export function mergeEvaluationDetails(details: any[]): any {
  if (details.length === 0) {
    return {};
  }
  
  if (details.length === 1) {
    return details[0] || {};
  }
  
  const merged: any = {};
  
  for (const detail of details) {
    if (detail && typeof detail === 'object') {
      Object.assign(merged, detail);
    }
  }
  
  return merged;
}

/**
 * Format evaluation summary for human-readable output
 */
export function formatEvaluationSummary(result: EvaluationResult): string {
  const lines: string[] = [];
  
  lines.push(`Strategy: ${result.strategy}`);
  lines.push(`Score: ${(result.score * 100).toFixed(1)}%`);
  lines.push(`Success: ${result.success ? 'Yes' : 'No'}`);
  
  if (result.executionTime) {
    lines.push(`Execution Time: ${result.executionTime}ms`);
  }
  
  if (result.error) {
    lines.push(`Error: ${result.error}`);
  }
  
  if (result.details) {
    lines.push('Details:');
    lines.push(formatDetails(result.details, 1));
  }
  
  return lines.join('\n');
}

/**
 * Format details object for display
 */
function formatDetails(details: any, indent: number = 0): string {
  const indentStr = '  '.repeat(indent);
  const lines: string[] = [];
  
  if (typeof details !== 'object' || details === null) {
    return `${indentStr}${String(details)}`;
  }
  
  for (const [key, value] of Object.entries(details)) {
    if (typeof value === 'object' && value !== null) {
      lines.push(`${indentStr}${key}:`);
      lines.push(formatDetails(value, indent + 1));
    } else {
      lines.push(`${indentStr}${key}: ${String(value)}`);
    }
  }
  
  return lines.join('\n');
}

/**
 * Calculate confidence score based on multiple factors
 */
export function calculateConfidence(result: EvaluationResult): number {
  let confidence = result.score;
  
  // Reduce confidence if there are errors
  if (result.error) {
    confidence *= 0.5;
  }
  
  // Increase confidence for successful results with details
  if (result.success && result.details) {
    confidence = Math.min(1.0, confidence * 1.1);
  }
  
  // Adjust based on execution time (very fast or very slow might be less reliable)
  if (result.executionTime) {
    if (result.executionTime < 100) {
      confidence *= 0.9; // Too fast might be superficial
    } else if (result.executionTime > 30000) {
      confidence *= 0.8; // Too slow might indicate issues
    }
  }
  
  return normalizeScore(confidence);
}

/**
 * Compare two evaluation results
 */
export function compareResults(result1: EvaluationResult, result2: EvaluationResult): number {
  // Primary comparison: score
  if (result1.score !== result2.score) {
    return result2.score - result1.score; // Higher score is better
  }
  
  // Secondary comparison: success
  if (result1.success !== result2.success) {
    return result1.success ? -1 : 1; // Success is better
  }
  
  // Tertiary comparison: execution time (faster is better)
  const time1 = result1.executionTime || 0;
  const time2 = result2.executionTime || 0;
  return time1 - time2;
}

/**
 * Filter results by minimum score threshold
 */
export function filterByScore(results: EvaluationResult[], minScore: number): EvaluationResult[] {
  return results.filter(result => result.score >= minScore);
}

/**
 * Filter results by success status
 */
export function filterBySuccess(results: EvaluationResult[], successOnly: boolean = true): EvaluationResult[] {
  return results.filter(result => result.success === successOnly);
}

/**
 * Get best result from a list of results
 */
export function getBestResult(results: EvaluationResult[]): EvaluationResult | null {
  if (results.length === 0) {
    return null;
  }
  
  return results.reduce((best, current) => 
    compareResults(best, current) > 0 ? current : best
  );
}

/**
 * Calculate score distribution statistics
 */
export function calculateScoreStats(results: EvaluationResult[]): {
  mean: number;
  median: number;
  min: number;
  max: number;
  stdDev: number;
} {
  if (results.length === 0) {
    return { mean: 0, median: 0, min: 0, max: 0, stdDev: 0 };
  }
  
  const scores = results.map(r => r.score).sort((a, b) => a - b);
  const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  const median = scores.length % 2 === 0 
    ? (scores[scores.length / 2 - 1] + scores[scores.length / 2]) / 2
    : scores[Math.floor(scores.length / 2)];
  const min = scores[0];
  const max = scores[scores.length - 1];
  
  const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
  const stdDev = Math.sqrt(variance);
  
  return { mean, median, min, max, stdDev };
}

/**
 * Create a score breakdown by strategy
 */
export function createScoreBreakdown(results: EvaluationResult[]): Record<string, number> {
  const breakdown: Record<string, number> = {};
  
  for (const result of results) {
    const strategy = result.strategy;
    if (!breakdown[strategy]) {
      breakdown[strategy] = 0;
    }
    breakdown[strategy] += result.score;
  }
  
  // Average the scores for each strategy
  const strategyCounts: Record<string, number> = {};
  for (const result of results) {
    const strategy = result.strategy;
    strategyCounts[strategy] = (strategyCounts[strategy] || 0) + 1;
  }
  
  for (const strategy in breakdown) {
    breakdown[strategy] /= strategyCounts[strategy];
  }
  
  return breakdown;
}
