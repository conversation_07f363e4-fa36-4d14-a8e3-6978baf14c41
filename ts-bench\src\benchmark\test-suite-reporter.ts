import { TestSuiteResult, ModelPerformanceResult, aggregateResultsByModel, calculateTestSuiteMetrics } from '../config/test-suite-types';
import { BenchmarkConfig } from '../config/types';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * TestSuiteBenchmarkReporter formats results in skatebench-compatible format
 */
export class TestSuiteBenchmarkReporter {
    
    /**
     * Print results to console in test suite format
     */
    printResults(results: TestSuiteResult[]): void {
        console.log('\n=== Test Suite Benchmark Results ===\n');
        
        const metrics = calculateTestSuiteMetrics(results);
        
        console.log('Overall Statistics:');
        console.log(`  Total Tests: ${metrics.totalTests}`);
        console.log(`  Correct: ${metrics.correctTests} (${metrics.successRate.toFixed(1)}%)`);
        console.log(`  Incorrect: ${metrics.incorrectTests}`);
        console.log(`  Errors: ${metrics.errorTests} (${metrics.errorRate.toFixed(1)}%)`);
        console.log(`  Average Duration: ${metrics.averageDuration.toFixed(0)}ms`);
        console.log(`  Total Cost: $${metrics.totalCost.toFixed(4)}`);
        console.log(`  Average Cost per Test: $${metrics.averageCost.toFixed(6)}`);
        
        // Group by suite
        const suiteGroups = new Map<string, TestSuiteResult[]>();
        for (const result of results) {
            if (!suiteGroups.has(result.suiteId)) {
                suiteGroups.set(result.suiteId, []);
            }
            suiteGroups.get(result.suiteId)!.push(result);
        }
        
        console.log('\nResults by Test Suite:');
        for (const [suiteId, suiteResults] of suiteGroups) {
            const suiteMetrics = calculateTestSuiteMetrics(suiteResults);
            console.log(`\n${suiteId}:`);
            console.log(`  Tests: ${suiteMetrics.totalTests}`);
            console.log(`  Success Rate: ${suiteMetrics.successRate.toFixed(1)}%`);
            console.log(`  Average Duration: ${suiteMetrics.averageDuration.toFixed(0)}ms`);
            
            // Show failed test cases
            const failedTests = suiteResults.filter(r => !r.correct || r.error);
            if (failedTests.length > 0) {
                console.log(`  Failed Tests:`);
                for (const failed of failedTests.slice(0, 5)) { // Show first 5 failures
                    console.log(`    - ${failed.testCaseId}: ${failed.error || 'Incorrect answer'}`);
                }
                if (failedTests.length > 5) {
                    console.log(`    ... and ${failedTests.length - 5} more`);
                }
            }
        }
    }

    /**
     * Print results in skatebench-style format
     */
    printSkatebenchResults(results: TestSuiteResult[], config: BenchmarkConfig): void {
        console.log('\n=== Skatebench Compatible Results ===\n');
        
        const modelPerformance = this.generateModelPerformance(results);
        
        // Sort by success rate descending
        modelPerformance.sort((a, b) => b.successRate - a.successRate);
        
        console.log('Model Rankings:');
        console.log('Rank | Model | Agent | Provider | Success Rate | Error Rate | Avg Duration | Total Cost | Tests');
        console.log('-'.repeat(100));
        
        modelPerformance.forEach((model, index) => {
            const rank = (index + 1).toString().padStart(4);
            const modelName = model.model.padEnd(15);
            const agent = model.agent.padEnd(8);
            const provider = model.provider.padEnd(10);
            const successRate = `${model.successRate.toFixed(1)}%`.padStart(10);
            const errorRate = `${model.errorRate.toFixed(1)}%`.padStart(9);
            const avgDuration = `${model.averageDuration.toFixed(0)}ms`.padStart(10);
            const totalCost = `$${model.totalCost.toFixed(4)}`.padStart(10);
            const tests = model.totalTests.toString().padStart(5);
            
            console.log(`${rank} | ${modelName} | ${agent} | ${provider} | ${successRate} | ${errorRate} | ${avgDuration} | ${totalCost} | ${tests}`);
        });
        
        // Summary statistics
        const totalTests = results.length;
        const totalCorrect = results.filter(r => r.correct && !r.error).length;
        const totalErrors = results.filter(r => r.error).length;
        const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);
        const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
        
        console.log('\nSummary:');
        console.log(`Total Test Cases: ${totalTests}`);
        console.log(`Overall Success Rate: ${((totalCorrect / totalTests) * 100).toFixed(1)}%`);
        console.log(`Overall Error Rate: ${((totalErrors / totalTests) * 100).toFixed(1)}%`);
        console.log(`Average Duration: ${avgDuration.toFixed(0)}ms`);
        console.log(`Total Cost: $${totalCost.toFixed(4)}`);
    }

    /**
     * Generate model performance data for rankings
     */
    generateModelPerformance(results: TestSuiteResult[]): ModelPerformanceResult[] {
        const modelMap = aggregateResultsByModel(results);
        return Array.from(modelMap.values());
    }

    /**
     * Export results to skatebench-compatible JSON format
     */
    async exportToSkatebenchJSON(
        results: TestSuiteResult[],
        config: BenchmarkConfig,
        outputPath: string
    ): Promise<void> {
        const modelPerformance = this.generateModelPerformance(results);
        
        // Sort by success rate descending
        modelPerformance.sort((a, b) => b.successRate - a.successRate);
        
        const skatebenchData = {
            metadata: {
                generatedAt: new Date().toISOString(),
                totalTestCases: results.length,
                totalModels: modelPerformance.length,
                benchmarkType: 'test-suite',
                version: '1.0.0'
            },
            rankings: modelPerformance.map((model, index) => ({
                rank: index + 1,
                model: model.model,
                agent: model.agent,
                provider: model.provider,
                metrics: {
                    successRate: parseFloat(model.successRate.toFixed(2)),
                    errorRate: parseFloat(model.errorRate.toFixed(2)),
                    averageDuration: parseFloat(model.averageDuration.toFixed(2)),
                    totalCost: parseFloat(model.totalCost.toFixed(6)),
                    averageCostPerTest: parseFloat(model.averageCostPerTest.toFixed(6))
                },
                counts: {
                    total: model.totalTests,
                    correct: model.correct,
                    incorrect: model.incorrect,
                    errors: model.errors
                }
            })),
            summary: {
                totalTests: results.length,
                totalCorrect: results.filter(r => r.correct && !r.error).length,
                totalIncorrect: results.filter(r => !r.correct && !r.error).length,
                totalErrors: results.filter(r => r.error).length,
                overallSuccessRate: parseFloat(((results.filter(r => r.correct && !r.error).length / results.length) * 100).toFixed(2)),
                overallErrorRate: parseFloat(((results.filter(r => r.error).length / results.length) * 100).toFixed(2)),
                totalCost: parseFloat(results.reduce((sum, r) => sum + (r.cost || 0), 0).toFixed(6)),
                averageDuration: parseFloat((results.reduce((sum, r) => sum + r.duration, 0) / results.length).toFixed(2))
            }
        };
        
        await fs.writeFile(outputPath, JSON.stringify(skatebenchData, null, 2));
    }

    /**
     * Save detailed test suite results
     */
    async saveTestSuiteResult(
        results: TestSuiteResult[],
        config: BenchmarkConfig,
        outputPath: string
    ): Promise<void> {
        const detailedResults = {
            metadata: {
                generatedAt: new Date().toISOString(),
                config: {
                    model: config.model,
                    agent: config.agent,
                    provider: config.provider,
                    useDocker: config.useDocker,
                    timeout: config.timeout
                },
                summary: calculateTestSuiteMetrics(results)
            },
            results: results.map(result => ({
                suiteId: result.suiteId,
                testCaseId: result.testCaseId,
                prompt: result.prompt,
                model: result.model,
                agent: result.agent,
                provider: result.provider,
                success: result.success,
                correct: result.correct,
                error: result.error,
                duration: result.duration,
                cost: result.cost,
                agentOutput: result.agentOutput,
                expectedOutput: result.expectedOutput,
                evaluationDetails: result.evaluationDetails,
                timestamp: result.timestamp.toISOString()
            }))
        };
        
        await fs.writeFile(outputPath, JSON.stringify(detailedResults, null, 2));
    }

    /**
     * Calculate and format metrics for display
     */
    calculateMetrics(results: TestSuiteResult[]): any {
        return calculateTestSuiteMetrics(results);
    }
}
