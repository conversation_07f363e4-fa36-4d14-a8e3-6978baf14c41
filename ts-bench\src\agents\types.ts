import type { Command } from '../execution/types';

export interface AgentBuilder {
    buildCommand(instructions: string, fileList?: FileList): Promise<Command>;

    /**
     * Builds a command for test suite execution using prompt-based tasks.
     * Use this method for test suites that provide prompts and expected answers.
     * @param prompt The test case prompt/question
     * @param systemPrompt The system-level instructions for the agent
     * @returns Promise<Command> for executing the agent with the combined prompts
     */
    buildTestSuiteCommand(prompt: string, systemPrompt: string): Promise<Command>;
}

export interface FileList {
    sourceFiles: string[];
    testFiles: string[];
}

export interface AgentConfig {
    model: string;
    provider?: string;
    containerName: string;
}
