import type { Command } from '../execution/types';
import type { FileList } from './types';

export abstract class BaseAgentBuilder {
  constructor(protected config: { containerName: string; model: string; provider?: string }) {}

  async buildCommand(instructions: string, fileList?: FileList): Promise<Command> {
    return {
      args: this.getCoreArgs(instructions, fileList),
      env: this.getEnvironmentVariables()
    };
  }

  async buildTestSuiteCommand(prompt: string, systemPrompt: string): Promise<Command> {
    // Combine system prompt and test case prompt with appropriate formatting
    const combinedInstructions = `${systemPrompt}\n\n${prompt}`;

    return {
      args: this.getCoreArgs(combinedInstructions, { sourceFiles: [], testFiles: [] }),
      env: this.getEnvironmentVariables()
    };
  }

  protected abstract getEnvironmentVariables(): Record<string, string>;
  protected abstract getCoreArgs(instructions: string, fileList?: FileList): string[];
}

