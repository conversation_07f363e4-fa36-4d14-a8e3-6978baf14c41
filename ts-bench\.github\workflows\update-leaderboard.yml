name: Propose Leaderboard Update from Artifact

on:
  workflow_dispatch:
    inputs:
      run_id:
        description: 'The ID of the workflow run that generated the benchmark artifact'
        required: true
        type: string
      artifact_name:
        description: 'The name of the artifact to download (e.g., benchmark-results)'
        required: true
        type: string
        default: 'benchmark-results'

run-name: 'Propose Leaderboard Update from Run ${{ github.event.inputs.run_id }}'

permissions:
  contents: write
  issues: write
  actions: read

concurrency:
  group: leaderboard-update
  cancel-in-progress: false

jobs:
  create-leaderboard-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Download benchmark artifact
        uses: dawidd6/action-download-artifact@v8
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          run_id: ${{ github.event.inputs.run_id }}
          name: ${{ github.event.inputs.artifact_name }}
          path: ./current-result

      - name: Find the result file path
        id: find_file
        run: |
          set -euo pipefail
          FILE_PATH=""
          if [ -f ./current-result/latest.json ]; then
            FILE_PATH=./current-result/latest.json
          else
            FILE_PATH=$(find ./current-result -type f -name 'latest.json' -print -quit || true)
            if [ -z "$FILE_PATH" ]; then
              FILE_PATH=$(find ./current-result -type f -name '*.json' -print -quit || true)
            fi
          fi
          if [ -z "$FILE_PATH" ]; then
            echo "::error::No JSON file found in artifact" >&2
            exit 1
          fi
          echo "Found result file: $FILE_PATH"
          echo "result_path=$FILE_PATH" >> $GITHUB_OUTPUT

      - name: Update Leaderboard and README files
        env:
          RUN_URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.event.inputs.run_id }}
          RUN_ID: ${{ github.event.inputs.run_id }}
          ARTIFACT_NAME: ${{ github.event.inputs.artifact_name }}
        run: bun scripts/update-leaderboard.ts ${{ steps.find_file.outputs.result_path }}
      - name: Prepare commit message file
        id: prepare_commit
        env:
          RUN_ID: ${{ github.event.inputs.run_id }}
        run: |
          set -euo pipefail
          TITLE="feat(leaderboard): Update with results from run ${RUN_ID}"
          printf "%s\n\n" "$TITLE" > commit-message.txt
          if [ -f commit-body.md ] && [ -s commit-body.md ]; then
            cat commit-body.md >> commit-message.txt
          else
            echo "No detailed diff generated." >> commit-message.txt
          fi
          echo "message_file=commit-message.txt" >> "$GITHUB_OUTPUT"

      - name: Commit and push changes to a new branch
        env:
          RUN_ID: ${{ github.event.inputs.run_id }}
        run: |
          set -euo pipefail
          BRANCH="leaderboard-update/${RUN_ID}"
          git config user.name "github-actions[bot]"
          git config user.email "41898282+github-actions[bot]@users.noreply.github.com"
          # Stage only expected files
          git add README.md public/data/leaderboard.json || true
          if git diff --cached --quiet; then
            echo "No changes to commit."
            exit 0
          fi
          git checkout -B "$BRANCH"
          git commit -F commit-message.txt
          git push -u origin "$BRANCH" --force-with-lease
