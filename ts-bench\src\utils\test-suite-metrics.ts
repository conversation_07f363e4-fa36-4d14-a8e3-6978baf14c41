import { TestSuiteResult, ModelPerformanceResult } from '../config/test-suite-types';

/**
 * Model pricing data for cost estimation
 */
interface ModelPricing {
    input: number; // cost per 1000 input tokens
    output: number; // cost per 1000 output tokens
}

/**
 * Pricing data for common models (cost per 1000 tokens)
 */
const MODEL_PRICING: Record<string, ModelPricing> = {
    // OpenAI models
    'gpt-4': { input: 0.03, output: 0.06 },
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
    'gpt-4o': { input: 0.005, output: 0.015 },
    'gpt-3.5-turbo': { input: 0.001, output: 0.002 },
    
    // Anthropic models
    'claude-3-opus': { input: 0.015, output: 0.075 },
    'claude-3-sonnet': { input: 0.003, output: 0.015 },
    'claude-3-haiku': { input: 0.00025, output: 0.00125 },
    'claude-3.5-sonnet': { input: 0.003, output: 0.015 },
    
    // Google models
    'gemini-pro': { input: 0.0005, output: 0.0015 },
    'gemini-1.5-pro': { input: 0.0035, output: 0.0105 },
    
    // Default fallback pricing
    'default': { input: 0.001, output: 0.002 }
};

/**
 * Estimate cost for a model based on input and output tokens
 */
export function estimateModelCost(
    model: string,
    provider: string,
    inputTokens: number,
    outputTokens: number
): number {
    const pricing = MODEL_PRICING[model] || MODEL_PRICING['default'];
    const inputCost = (inputTokens / 1000) * pricing.input;
    const outputCost = (outputTokens / 1000) * pricing.output;
    return inputCost + outputCost;
}

/**
 * Estimate token usage from text (rough approximation)
 */
export function calculateTokenUsage(prompt: string, response: string): { inputTokens: number; outputTokens: number } {
    // Rough approximation: 1 token ≈ 4 characters for English text
    const inputTokens = Math.ceil(prompt.length / 4);
    const outputTokens = Math.ceil(response.length / 4);
    
    return { inputTokens, outputTokens };
}

/**
 * Aggregate test suite results by model for ranking
 */
export function aggregateResultsByModel(results: TestSuiteResult[]): Map<string, TestSuiteResult[]> {
    const modelMap = new Map<string, TestSuiteResult[]>();
    
    for (const result of results) {
        const modelKey = `${result.model}-${result.agent}-${result.provider}`;
        
        if (!modelMap.has(modelKey)) {
            modelMap.set(modelKey, []);
        }
        
        modelMap.get(modelKey)!.push(result);
    }
    
    return modelMap;
}

/**
 * Calculate success rate as percentage
 */
export function calculateSuccessRate(correct: number, total: number): number {
    if (total === 0) return 0;
    return (correct / total) * 100;
}

/**
 * Calculate error rate as percentage
 */
export function calculateErrorRate(errors: number, total: number): number {
    if (total === 0) return 0;
    return (errors / total) * 100;
}

/**
 * Format duration for skatebench compatibility
 */
export function formatDurationForSkatebench(duration: number): number {
    // Convert milliseconds to seconds with 2 decimal places
    return parseFloat((duration / 1000).toFixed(2));
}

/**
 * Generate model ranking from aggregated results
 */
export function generateModelRanking(modelResults: Map<string, TestSuiteResult[]>): ModelPerformanceResult[] {
    const rankings: ModelPerformanceResult[] = [];
    
    for (const [modelKey, results] of modelResults) {
        const firstResult = results[0];
        const totalTests = results.length;
        const correctTests = results.filter(r => r.correct && !r.error).length;
        const errorTests = results.filter(r => r.error).length;
        const incorrectTests = totalTests - correctTests - errorTests;
        
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);
        
        const ranking: ModelPerformanceResult = {
            model: firstResult.model,
            agent: firstResult.agent,
            provider: firstResult.provider,
            correct: correctTests,
            incorrect: incorrectTests,
            errors: errorTests,
            totalTests,
            successRate: calculateSuccessRate(correctTests, totalTests),
            errorRate: calculateErrorRate(errorTests, totalTests),
            averageDuration: totalDuration / totalTests,
            totalCost,
            averageCostPerTest: totalCost / totalTests,
            testResults: results
        };
        
        rankings.push(ranking);
    }
    
    // Sort by success rate descending, then by error rate ascending
    rankings.sort((a, b) => {
        if (Math.abs(a.successRate - b.successRate) < 0.1) {
            return a.errorRate - b.errorRate;
        }
        return b.successRate - a.successRate;
    });
    
    return rankings;
}

/**
 * Validate skatebench output format
 */
export function validateSkatebenchOutput(output: any): boolean {
    try {
        // Check required top-level fields
        if (!output.metadata || !output.rankings || !output.summary) {
            return false;
        }
        
        // Check metadata structure
        const metadata = output.metadata;
        if (!metadata.generatedAt || !metadata.totalTestCases || !metadata.benchmarkType) {
            return false;
        }
        
        // Check rankings structure
        if (!Array.isArray(output.rankings)) {
            return false;
        }
        
        for (const ranking of output.rankings) {
            if (!ranking.model || !ranking.agent || !ranking.provider || 
                !ranking.metrics || !ranking.counts) {
                return false;
            }
            
            // Check metrics
            const metrics = ranking.metrics;
            if (typeof metrics.successRate !== 'number' || 
                typeof metrics.errorRate !== 'number' ||
                typeof metrics.averageDuration !== 'number' ||
                typeof metrics.totalCost !== 'number') {
                return false;
            }
            
            // Check counts
            const counts = ranking.counts;
            if (typeof counts.total !== 'number' ||
                typeof counts.correct !== 'number' ||
                typeof counts.incorrect !== 'number' ||
                typeof counts.errors !== 'number') {
                return false;
            }
        }
        
        // Check summary structure
        const summary = output.summary;
        if (typeof summary.totalTests !== 'number' ||
            typeof summary.overallSuccessRate !== 'number' ||
            typeof summary.overallErrorRate !== 'number' ||
            typeof summary.totalCost !== 'number') {
            return false;
        }
        
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Calculate percentile for a given value in a dataset
 */
export function calculatePercentile(values: number[], percentile: number): number {
    const sorted = [...values].sort((a, b) => a - b);
    const index = (percentile / 100) * (sorted.length - 1);
    
    if (index === Math.floor(index)) {
        return sorted[index];
    }
    
    const lower = sorted[Math.floor(index)];
    const upper = sorted[Math.ceil(index)];
    const weight = index - Math.floor(index);
    
    return lower + (upper - lower) * weight;
}

/**
 * Calculate statistical summary for a set of results
 */
export function calculateStatisticalSummary(results: TestSuiteResult[]): {
    mean: number;
    median: number;
    p95: number;
    p99: number;
    min: number;
    max: number;
    stdDev: number;
} {
    const durations = results.map(r => r.duration);
    const mean = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    
    // Calculate standard deviation
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - mean, 2), 0) / durations.length;
    const stdDev = Math.sqrt(variance);
    
    return {
        mean,
        median: calculatePercentile(durations, 50),
        p95: calculatePercentile(durations, 95),
        p99: calculatePercentile(durations, 99),
        min: Math.min(...durations),
        max: Math.max(...durations),
        stdDev
    };
}
