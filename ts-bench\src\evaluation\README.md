# Evaluation System

The evaluation system provides comprehensive assessment of coding agent outputs using multiple evaluation strategies. It supports pattern matching (similar to skatebench), file validation, compilation checking, and basic functionality testing.

## Architecture

The evaluation system is built around the following core components:

- **EvaluationService**: Main service that orchestrates evaluation
- **Evaluators**: Individual strategy implementations (Pattern, File, Compilation, Functionality)
- **CompositeEvaluator**: Combines multiple strategies with weighted scoring
- **Utilities**: Helper functions for text processing, file operations, and scoring

## Evaluation Strategies

### 1. Pattern Evaluation (`PatternEvaluator`)

Checks agent output against expected answers and negative answers using various matching modes:

- **Contains**: Check if output contains the expected text
- **Exact**: Exact string matching
- **Case Insensitive**: Case-insensitive contains matching
- **Regex**: Regular expression matching

```typescript
const evaluator = new PatternEvaluator({
  matchMode: MatchMode.CASE_INSENSITIVE,
  normalize: true
});
```

### 2. File Evaluation (`FileEvaluator`)

Validates that the agent created the expected files with proper structure:

- Checks for required and optional files
- Validates file extensions and naming conventions
- Basic content validation (non-empty, contains imports/exports)
- Directory structure validation

```typescript
const evaluator = new FileEvaluator({
  requiredExtensions: ['.ts', '.tsx'],
  checkContent: true,
  minFileSize: 1
});
```

### 3. Compilation Evaluation (`CompilationEvaluator`)

Verifies that generated code compiles successfully:

- TypeScript/JavaScript syntax checking
- Type checking (optional)
- Compilation error reporting
- Support for different compiler targets

```typescript
const evaluator = new CompilationEvaluator({
  compilerOptions: {
    target: 'ES2020',
    strict: false
  },
  typeCheck: false
});
```

### 4. Functionality Evaluation (`FunctionalityEvaluator`)

Performs basic runtime testing of generated code:

- React component validation
- Express server validation
- Function export testing
- Basic syntax checking

```typescript
const evaluator = new FunctionalityEvaluator({
  scenarios: [customTestScenario],
  defaultTimeout: 10000
});
```

### 5. Composite Evaluation (`CompositeEvaluator`)

Combines multiple strategies with weighted scoring:

```typescript
const evaluator = new CompositeEvaluator({
  strategies: [
    EvaluationStrategy.PATTERN,
    EvaluationStrategy.FILE,
    EvaluationStrategy.COMPILATION
  ],
  weights: {
    [EvaluationStrategy.PATTERN]: 0.5,
    [EvaluationStrategy.FILE]: 0.3,
    [EvaluationStrategy.COMPILATION]: 0.2
  }
});
```

## Usage Examples

### Basic Pattern Matching

```typescript
import { EvaluationService, EvaluationStrategy } from './evaluation';

const service = new EvaluationService();
const context = {
  testCase: {
    expectedAnswers: ['React component', 'useState'],
    negativeAnswers: ['class component']
  },
  agentOutput: 'Here is a React component using useState hook...',
  workspacePath: '/tmp/workspace'
};

const result = await service.evaluateAgentOutput(context, {
  strategies: [EvaluationStrategy.PATTERN]
});

console.log(`Score: ${result.score}, Success: ${result.success}`);
```

### File and Compilation Checking

```typescript
const config = {
  strategies: [EvaluationStrategy.FILE, EvaluationStrategy.COMPILATION],
  weights: {
    [EvaluationStrategy.FILE]: 0.6,
    [EvaluationStrategy.COMPILATION]: 0.4
  }
};

const result = await service.evaluateAgentOutput(context, config);
```

### Comprehensive Evaluation

```typescript
const result = await comprehensiveEvaluate(
  testCase,
  agentOutput,
  workspacePath
);

if (hasMetrics(result)) {
  const metrics = result.details.metrics;
  console.log(`Overall Score: ${metrics.overallScore}`);
  console.log(`Success Rate: ${metrics.successCount}/${metrics.successCount + metrics.failureCount}`);
}
```

## Configuration

### Test Case Format

Test cases should include evaluation criteria:

```typescript
const testCase = {
  // Pattern matching
  expectedAnswers: ['React', 'component'],
  negativeAnswers: ['class extends'],
  
  // File validation
  requiredFiles: ['App.tsx', 'index.ts'],
  optionalFiles: ['styles.css'],
  
  // Custom scenarios
  testScenarios: [
    {
      name: 'Component renders',
      test: async (workspace) => {
        // Custom test logic
        return true;
      }
    }
  ]
};
```

### Evaluation Configuration

```typescript
const config: EvaluationConfig = {
  strategies: [EvaluationStrategy.PATTERN, EvaluationStrategy.FILE],
  weights: {
    [EvaluationStrategy.PATTERN]: 0.7,
    [EvaluationStrategy.FILE]: 0.3
  },
  timeout: 30000,
  failFast: false,
  strategyConfig: {
    [EvaluationStrategy.PATTERN]: {
      matchMode: MatchMode.CASE_INSENSITIVE,
      normalize: true
    },
    [EvaluationStrategy.FILE]: {
      requiredExtensions: ['.ts', '.tsx'],
      checkContent: true
    }
  }
};
```

## Best Practices

### Writing Effective Test Cases

1. **Clear Expected Answers**: Use specific, unambiguous patterns
2. **Negative Patterns**: Include what should NOT appear in correct answers
3. **File Requirements**: Specify required files and directory structure
4. **Balanced Weights**: Adjust strategy weights based on task importance

### Performance Optimization

1. **Use Caching**: The EvaluationService automatically caches results
2. **Appropriate Timeouts**: Set reasonable timeouts for each strategy
3. **Fail Fast**: Enable fail-fast for critical strategies
4. **Batch Processing**: Use `batchEvaluate` for multiple test cases

### Error Handling

1. **Graceful Degradation**: Individual strategy failures don't break overall evaluation
2. **Detailed Error Reporting**: Check `result.error` and `result.details` for diagnostics
3. **Validation**: Validate contexts and configurations before evaluation

## Troubleshooting

### Common Issues

1. **No Expected Answers**: Ensure test cases have `expectedAnswers`, `answers`, or `expected` fields
2. **Workspace Not Found**: Verify workspace path exists and is accessible
3. **Compilation Errors**: Check TypeScript configuration and dependencies
4. **Timeout Issues**: Increase timeout values for complex evaluations

### Debugging

Enable debug logging to see detailed evaluation steps:

```typescript
import { logger } from '../utils/logger';
logger.setLevel('debug');
```

### Performance Issues

- Clear cache periodically: `service.clearCache()`
- Use appropriate strategy combinations
- Consider parallel vs sequential evaluation modes

## Integration with Test Suite System

The evaluation system integrates with the existing test suite infrastructure:

```typescript
// In TestSuiteTestRunner
const evaluationService = new EvaluationService();
const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
const result = await evaluationService.evaluateAgentOutput(context);

// Convert to AgentResult format
const agentResult = {
  success: result.success,
  score: result.score,
  details: result.details,
  error: result.error
};
```

## Extending the System

### Custom Evaluators

Create custom evaluators by extending the `Evaluator` base class:

```typescript
class CustomEvaluator extends Evaluator {
  constructor() {
    super(EvaluationStrategy.CUSTOM);
  }
  
  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    // Custom evaluation logic
    return {
      score: 1.0,
      success: true,
      strategy: this.strategy
    };
  }
}
```

### Custom Test Scenarios

Add custom functionality tests:

```typescript
const customScenario: TestScenario = {
  name: 'API Endpoint Test',
  test: async (workspace) => {
    // Test API endpoint functionality
    return true;
  },
  timeout: 15000,
  required: true
};
```
