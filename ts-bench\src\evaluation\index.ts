/**
 * Evaluation System for TS-Bench
 *
 * This module provides a comprehensive evaluation system for assessing coding agent outputs
 * using multiple evaluation strategies including pattern matching, file validation,
 * compilation checking, and basic functionality testing.
 *
 * @example
 * ```typescript
 * import { EvaluationService, EvaluationStrategy } from './evaluation';
 *
 * const service = new EvaluationService();
 * const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
 * const config = EvaluationService.createDefaultConfig('comprehensive');
 *
 * const result = await service.evaluateAgentOutput(context, config);
 * console.log(`Score: ${result.score}, Success: ${result.success}`);
 * ```
 */

import * as ts from 'typescript';

// Core types and interfaces
export type {
  EvaluationResult,
  EvaluationContext,
  EvaluationConfig,
  EvaluationMetrics,
  EvaluationCriteria,
  Evaluator,
  PatternMatch,
  FileStructure,
  CompilationConfig,
  TestScenario
} from './types';

export {
  EvaluationStrategy,
  MatchMode
} from './types';

// Main evaluation service
export { EvaluationService } from './evaluation-service';

// Individual evaluators
export { PatternEvaluator } from './evaluators/pattern-evaluator';
export { FileEvaluator } from './evaluators/file-evaluator';
export { CompilationEvaluator } from './evaluators/compilation-evaluator';
export { FunctionalityEvaluator } from './evaluators/functionality-evaluator';
export { CompositeEvaluator } from './evaluators/composite-evaluator';

// Utility functions
export {
  normalizeText,
  extractCodeBlocks,
  detectLanguage,
  sanitizeCode,
  findImports,
  findExports,
  matchPattern,
  scorePatternMatch,
  extractKeyTerms,
  calculateTextSimilarity,
  containsCode,
  cleanForComparison
} from './utils/text-utils';

export {
  createTempWorkspace,
  cleanupWorkspace,
  copyAgentOutput,
  validateFileStructure,
  findGeneratedFiles,
  readFileContent,
  writeTestFile,
  resolveWorkspacePath,
  isSafePath,
  getFileStats,
  isFileReadable,
  createDirectoryStructure,
  copyFile,
  getDirectorySize,
  listFiles
} from './utils/file-utils';

export {
  calculateScore,
  aggregateResults,
  normalizeScore,
  weightedAverage,
  createSuccessResult,
  createFailureResult,
  mergeEvaluationDetails,
  formatEvaluationSummary,
  calculateConfidence,
  compareResults,
  filterByScore,
  filterBySuccess,
  getBestResult,
  calculateScoreStats,
  createScoreBreakdown
} from './utils/scoring-utils';

/**
 * Factory function to create a pattern evaluator with common configurations
 */
export function createPatternEvaluator(mode: MatchMode = MatchMode.CASE_INSENSITIVE) {
  return new PatternEvaluator({
    matchMode: mode,
    normalize: true,
    positiveWeight: 1.0,
    negativeWeight: -1.0
  });
}

/**
 * Factory function to create a file evaluator with common configurations
 */
export function createFileEvaluator(extensions: string[] = ['.ts', '.tsx', '.js', '.jsx']) {
  return new FileEvaluator({
    requiredExtensions: extensions,
    checkContent: true,
    minFileSize: 1
  });
}

/**
 * Factory function to create a compilation evaluator with TypeScript configuration
 */
export function createCompilationEvaluator(strict: boolean = false) {
  return new CompilationEvaluator({
    compilerOptions: {
      target: ts.ScriptTarget.ES2020,
      module: ts.ModuleKind.CommonJS,
      strict,
      esModuleInterop: true,
      skipLibCheck: true
    },
    typeCheck: strict,
    timeout: 30000
  });
}

/**
 * Factory function to create a functionality evaluator with basic scenarios
 */
export function createFunctionalityEvaluator() {
  return new FunctionalityEvaluator({
    scenarios: [],
    defaultTimeout: 10000,
    useSandbox: true
  });
}

/**
 * Factory function to create a composite evaluator with balanced configuration
 */
export function createCompositeEvaluator(strategies: EvaluationStrategy[] = [
  EvaluationStrategy.PATTERN,
  EvaluationStrategy.FILE
]) {
  const config: EvaluationConfig = {
    strategies,
    weights: {
      [EvaluationStrategy.PATTERN]: 0.5,
      [EvaluationStrategy.FILE]: 0.3,
      [EvaluationStrategy.COMPILATION]: 0.15,
      [EvaluationStrategy.FUNCTIONALITY]: 0.05,
      [EvaluationStrategy.COMPOSITE]: 1.0
    },
    timeout: 45000,
    failFast: false
  };
  
  return new CompositeEvaluator(config);
}

/**
 * Convenience function to quickly evaluate agent output with default settings
 */
export async function quickEvaluate(
  testCase: any,
  agentOutput: string,
  workspacePath: string
): Promise<EvaluationResult> {
  const service = new EvaluationService();
  const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
  const config = EvaluationService.createDefaultConfig('pattern');
  
  return service.evaluateAgentOutput(context, config);
}

/**
 * Convenience function to evaluate with file checking
 */
export async function evaluateWithFiles(
  testCase: any,
  agentOutput: string,
  workspacePath: string
): Promise<EvaluationResult> {
  const service = new EvaluationService();
  const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
  const config = EvaluationService.createDefaultConfig('file');
  
  return service.evaluateAgentOutput(context, config);
}

/**
 * Convenience function to evaluate with compilation checking
 */
export async function evaluateWithCompilation(
  testCase: any,
  agentOutput: string,
  workspacePath: string
): Promise<EvaluationResult> {
  const service = new EvaluationService();
  const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
  const config = EvaluationService.createDefaultConfig('compilation');
  
  return service.evaluateAgentOutput(context, config);
}

/**
 * Convenience function to perform comprehensive evaluation
 */
export async function comprehensiveEvaluate(
  testCase: any,
  agentOutput: string,
  workspacePath: string
): Promise<EvaluationResult> {
  const service = new EvaluationService();
  const context = EvaluationService.createContext(testCase, agentOutput, workspacePath);
  const config = EvaluationService.createDefaultConfig('comprehensive');
  
  return service.evaluateAgentOutput(context, config);
}

/**
 * Type guard to check if a result is from a composite evaluator
 */
export function isCompositeResult(result: EvaluationResult): boolean {
  return result.strategy === EvaluationStrategy.COMPOSITE && 
         result.details?.metrics?.strategyResults !== undefined;
}

/**
 * Type guard to check if a result has detailed metrics
 */
export function hasMetrics(result: EvaluationResult): result is EvaluationResult & {
  details: { metrics: EvaluationMetrics }
} {
  return result.details?.metrics !== undefined;
}

/**
 * Extract individual strategy results from a composite result
 */
export function extractStrategyResults(result: EvaluationResult): Record<EvaluationStrategy, EvaluationResult> | null {
  if (!isCompositeResult(result)) {
    return null;
  }
  
  return result.details.metrics.strategyResults;
}

/**
 * Get the primary evaluation strategy used
 */
export function getPrimaryStrategy(result: EvaluationResult): EvaluationStrategy {
  if (result.strategy === EvaluationStrategy.COMPOSITE && hasMetrics(result)) {
    // Find the strategy with the highest weight or score
    const strategyResults = result.details.metrics.strategyResults;
    let bestStrategy = EvaluationStrategy.PATTERN;
    let bestScore = -1;
    
    for (const [strategy, strategyResult] of Object.entries(strategyResults)) {
      if (strategyResult.score > bestScore) {
        bestScore = strategyResult.score;
        bestStrategy = strategy as EvaluationStrategy;
      }
    }
    
    return bestStrategy;
  }
  
  return result.strategy;
}
