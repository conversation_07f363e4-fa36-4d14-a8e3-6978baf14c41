/**
 * Test Suites Module
 * 
 * A new test suite system that combines skatebench's simple JSON format
 * with ts-bench's coding agent evaluation capabilities.
 * 
 * This module provides:
 * - TypeScript interfaces for test suite structure
 * - TestSuiteReader for loading and managing test suites
 * - Utility functions for validation and ID generation
 * - Sample test suites for React and Express.js
 */

// Export all types
export * from './types';

// Export the main reader class and logger interface
export { TestSuiteReader, TestSuiteLogger } from './reader';

// Export utility functions
export * from './utils';

// Convenience exports for common operations
export { TestSuiteReader as Reader } from './reader';

// Import for use in convenience functions
import { TestSuiteLogger } from './reader';

/**
 * Create a new TestSuiteReader instance with default configuration
 * @param baseDir Optional base directory for test suites (defaults to samples directory)
 * @param logger Optional logger for warnings and errors
 * @returns Configured TestSuiteReader instance
 */
export function createReader(baseDir?: string, logger?: TestSuiteLogger): TestSuiteReader {
  return new TestSuiteReader(baseDir, logger);
}

/**
 * Quick function to load a test suite by ID using default configuration
 * @param id Test suite identifier
 * @param baseDir Optional base directory override
 * @param logger Optional logger for warnings and errors
 * @returns Promise resolving to loaded test suite
 */
export async function loadSuite(id: string, baseDir?: string, logger?: TestSuiteLogger) {
  const reader = createReader(baseDir, logger);
  return reader.loadSuite(id);
}

/**
 * Quick function to list all available test suites using default configuration
 * @param baseDir Optional base directory override
 * @param logger Optional logger for warnings and errors
 * @returns Promise resolving to array of test suite IDs
 */
export async function listSuites(baseDir?: string, logger?: TestSuiteLogger): Promise<string[]> {
  const reader = createReader(baseDir, logger);
  return reader.listSuites();
}

// Re-export TestSuiteReader for backwards compatibility
import { TestSuiteReader } from './reader';
export default TestSuiteReader;
