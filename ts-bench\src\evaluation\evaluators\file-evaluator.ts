import { existsSync, statSync } from 'fs';
import { join, normalize } from 'path';
import { Evaluator, EvaluationContext, EvaluationResult, EvaluationStrategy, FileStructure } from '../types';
import { validateFileStructure, findGeneratedFiles, readFileContent } from '../utils/file-utils';

/**
 * Configuration for file evaluation
 */
export interface FileEvaluatorConfig {
  /** Required file extensions */
  requiredExtensions?: string[];
  /** Minimum file size in bytes */
  minFileSize?: number;
  /** Maximum file size in bytes */
  maxFileSize?: number;
  /** Whether to check file content */
  checkContent?: boolean;
  /** Required content patterns */
  requiredPatterns?: string[];
  /** File naming conventions */
  namingConventions?: RegExp[];
}

/**
 * File-based evaluator for checking if coding agents created expected files
 */
export class FileEvaluator extends Evaluator {
  private config: FileEvaluatorConfig;

  constructor(config: FileEvaluatorConfig = {}) {
    super(EvaluationStrategy.FILE);
    this.config = {
      requiredExtensions: ['.ts', '.js', '.tsx', '.jsx'],
      minFileSize: 1,
      maxFileSize: 1024 * 1024, // 1MB
      checkContent: true,
      requiredPatterns: [],
      namingConventions: [],
      ...config
    };
  }

  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    const startTime = Date.now();
    
    try {
      const { testCase, workspacePath } = context;
      
      // Extract file requirements from test case
      const fileStructure = this.extractFileStructure(testCase);
      
      if (!existsSync(workspacePath)) {
        return {
          score: 0,
          success: false,
          error: `Workspace path does not exist: ${workspacePath}`,
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Validate file structure
      const structureResult = await validateFileStructure(workspacePath, fileStructure);
      
      // Find all generated files
      const generatedFiles = await findGeneratedFiles(
        workspacePath, 
        this.config.requiredExtensions || []
      );

      // Evaluate individual files
      const fileResults = await this.evaluateFiles(workspacePath, generatedFiles);
      
      // Calculate overall score
      const score = this.calculateFileScore(structureResult, fileResults, fileStructure);
      
      return {
        score,
        success: score > 0.5, // Consider successful if more than half the requirements are met
        details: {
          structureResult,
          fileResults,
          generatedFiles,
          requiredFiles: fileStructure.requiredFiles,
          optionalFiles: fileStructure.optionalFiles
        },
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        score: 0,
        success: false,
        error: `File evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Extract file structure requirements from test case
   */
  private extractFileStructure(testCase: any): FileStructure {
    const structure: FileStructure = {
      requiredFiles: [],
      optionalFiles: [],
      requiredDirectories: [],
      namingPatterns: {}
    };

    // Extract from various possible formats
    if (testCase.files) {
      if (Array.isArray(testCase.files)) {
        structure.requiredFiles = testCase.files;
      } else if (typeof testCase.files === 'object') {
        structure.requiredFiles = testCase.files.required || [];
        structure.optionalFiles = testCase.files.optional || [];
        structure.requiredDirectories = testCase.files.directories || [];
      }
    }

    if (testCase.requiredFiles) {
      structure.requiredFiles = Array.isArray(testCase.requiredFiles) 
        ? testCase.requiredFiles 
        : [testCase.requiredFiles];
    }

    if (testCase.expectedFiles) {
      structure.requiredFiles = Array.isArray(testCase.expectedFiles) 
        ? testCase.expectedFiles 
        : [testCase.expectedFiles];
    }

    // Infer files from task description if no explicit requirements
    if (structure.requiredFiles.length === 0) {
      structure.requiredFiles = this.inferRequiredFiles(testCase);
    }

    return structure;
  }

  /**
   * Infer required files from task description
   */
  private inferRequiredFiles(testCase: any): string[] {
    const files: string[] = [];
    const description = testCase.description || testCase.prompt || '';
    
    // Look for common patterns in task descriptions
    const patterns = [
      /create.*?(\w+\.(?:ts|js|tsx|jsx))/gi,
      /implement.*?(\w+\.(?:ts|js|tsx|jsx))/gi,
      /build.*?(\w+\.(?:ts|js|tsx|jsx))/gi,
      /file.*?(\w+\.(?:ts|js|tsx|jsx))/gi
    ];

    for (const pattern of patterns) {
      const matches = description.matchAll(pattern);
      for (const match of matches) {
        if (match[1]) {
          files.push(match[1]);
        }
      }
    }

    return files;
  }

  /**
   * Evaluate individual files
   */
  private async evaluateFiles(workspacePath: string, files: string[]): Promise<any[]> {
    const results = [];

    for (const file of files) {
      const filePath = join(workspacePath, file);
      const result = await this.evaluateFile(filePath);
      results.push({ file, ...result });
    }

    return results;
  }

  /**
   * Evaluate a single file
   */
  private async evaluateFile(filePath: string): Promise<any> {
    try {
      if (!existsSync(filePath)) {
        return { exists: false, score: 0 };
      }

      const stats = statSync(filePath);
      const size = stats.size;
      
      // Check file size
      if (this.config.minFileSize && size < this.config.minFileSize) {
        return { exists: true, score: 0.2, reason: 'File too small' };
      }
      
      if (this.config.maxFileSize && size > this.config.maxFileSize) {
        return { exists: true, score: 0.2, reason: 'File too large' };
      }

      let contentScore = 1.0;
      
      // Check content if configured
      if (this.config.checkContent) {
        const content = await readFileContent(filePath);
        contentScore = this.evaluateFileContent(content);
      }

      return {
        exists: true,
        size,
        score: Math.min(1.0, contentScore),
        contentScore
      };

    } catch (error) {
      return {
        exists: false,
        score: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Evaluate file content
   */
  private evaluateFileContent(content: string): number {
    let score = 0.5; // Base score for non-empty file
    
    if (!content || content.trim().length === 0) {
      return 0;
    }

    // Check for required patterns
    if (this.config.requiredPatterns) {
      const patternMatches = this.config.requiredPatterns.filter(pattern => 
        content.includes(pattern)
      );
      score += (patternMatches.length / this.config.requiredPatterns.length) * 0.5;
    }

    // Basic code quality checks
    if (content.includes('import') || content.includes('require')) {
      score += 0.1;
    }
    
    if (content.includes('export')) {
      score += 0.1;
    }

    if (content.includes('function') || content.includes('=>')) {
      score += 0.1;
    }

    return Math.min(1.0, score);
  }

  /**
   * Calculate overall file score
   */
  private calculateFileScore(
    structureResult: any, 
    fileResults: any[], 
    fileStructure: FileStructure
  ): number {
    let score = 0;
    let totalWeight = 0;

    // Score required files (higher weight)
    const requiredWeight = 0.8;
    for (const requiredFile of fileStructure.requiredFiles) {
      // Normalize paths for cross-platform compatibility
      const normalizedRequiredFile = normalize(requiredFile);
      const fileResult = fileResults.find(r => normalize(r.file) === normalizedRequiredFile);
      score += (fileResult?.score || 0) * requiredWeight;
      totalWeight += requiredWeight;
    }

    // Score optional files (lower weight)
    const optionalWeight = 0.2;
    for (const optionalFile of fileStructure.optionalFiles || []) {
      // Normalize paths for cross-platform compatibility
      const normalizedOptionalFile = normalize(optionalFile);
      const fileResult = fileResults.find(r => normalize(r.file) === normalizedOptionalFile);
      if (fileResult) {
        score += fileResult.score * optionalWeight;
        totalWeight += optionalWeight;
      }
    }

    return totalWeight > 0 ? score / totalWeight : 0;
  }
}
