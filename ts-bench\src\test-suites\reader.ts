import fs from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { TestSuite, LoadedSuite, LoadedTestCase, LoadOptions, ValidationResult } from './types';
import {
  validateTestSuite,
  computeTestSignature,
  signatureHash,
  extractSuiteId,
  normalizeAnswers
} from './utils';

/**
 * Logger interface for TestSuiteReader
 */
export interface TestSuiteLogger {
  warn(message: string): void;
}

/**
 * Reader class for loading and managing test suites
 * Follows the pattern from ts-bench exercises reader
 */
export class TestSuiteReader {
  private baseDir: string;
  private idToFilenameMap: Map<string, string> = new Map();
  private logger?: TestSuiteLogger;

  constructor(baseDir?: string, logger?: TestSuiteLogger) {
    this.logger = logger;
    if (baseDir) {
      this.baseDir = baseDir;
    } else {
      // Try to find the first existing directory from candidates
      const candidates = [
        path.join(__dirname, 'samples'),
        path.resolve(process.cwd(), 'ts-bench/src/test-suites/samples'),
        path.resolve(process.cwd(), 'ts-bench/dist/test-suites/samples'),
      ];

      let foundDir: string | undefined;
      for (const dir of candidates) {
        if (existsSync(dir)) {
          foundDir = dir;
          break;
        }
      }

      if (!foundDir) {
        throw new Error('Test suites directory not found. Pass baseDir to TestSuiteReader constructor.');
      }

      this.baseDir = foundDir;
    }
  }

  /**
   * List all available test suite IDs
   * Discovers .json files in the test suites directory
   */
  async listSuites(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.baseDir);
      const jsonFiles = files.filter(file => file.toLowerCase().endsWith('.json'));

      // Build mapping from ID to actual filename
      this.idToFilenameMap.clear();
      const suiteIds: string[] = [];

      for (const file of jsonFiles) {
        const id = extractSuiteId(file);
        this.idToFilenameMap.set(id, file);
        suiteIds.push(id);
      }

      return suiteIds.sort();
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error(`Test suites directory not found: ${this.baseDir}`);
      }
      throw new Error(`Failed to list test suites: ${(error as Error).message}`);
    }
  }

  /**
   * Load a specific test suite by ID
   * Parses JSON, validates structure, and computes test IDs
   */
  async loadSuite(id: string, options: LoadOptions = {}): Promise<LoadedSuite> {
    const {
      validate = true,
      computeIds = true,
      baseDir = this.baseDir
    } = options;

    // Use the mapping if available, otherwise fallback to direct filename
    const filename = this.idToFilenameMap.get(id) || `${id}.json`;
    const filePath = path.resolve(baseDir, filename);

    try {
      // Read and parse JSON file
      const content = await fs.readFile(filePath, 'utf-8');
      let suite: TestSuite;
      
      try {
        suite = JSON.parse(content);
      } catch (parseError) {
        throw new Error(`Invalid JSON in test suite '${id}': ${(parseError as Error).message}`);
      }

      // Validate structure if requested
      if (validate) {
        const validation = this.validateSuite(suite);
        if (!validation.valid) {
          throw new Error(`Invalid test suite '${id}':\n${validation.errors.join('\n')}`);
        }
        
        // Log warnings if present
        if (validation.warnings.length > 0) {
          this.logger?.warn(`Warnings for test suite '${id}':\n${validation.warnings.join('\n')}`);
        }
      }

      // Create loaded suite with computed IDs
      const loadedSuite: LoadedSuite = {
        ...suite,
        id,
        tests: computeIds ? this.computeTestIds(suite) : suite.tests as LoadedTestCase[]
      };

      return loadedSuite;
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error(`Test suite not found: ${id} (looked for ${filePath})`);
      }
      throw error;
    }
  }

  /**
   * Validate a test suite structure
   * Returns detailed validation results with errors and warnings
   */
  validateSuite(suite: any): ValidationResult {
    return validateTestSuite(suite);
  }

  /**
   * Check if a test suite exists
   * Returns true if the JSON file exists and is readable
   */
  async suiteExists(id: string): Promise<boolean> {
    // Use the mapping if available, otherwise fallback to direct filename
    const filename = this.idToFilenameMap.get(id) || `${id}.json`;
    const filePath = path.resolve(this.baseDir, filename);

    try {
      await fs.access(filePath, fs.constants.R_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get basic information about a test suite without loading all tests
   * Useful for listing suites with metadata
   */
  async getSuiteInfo(id: string): Promise<{ name: string; description: string; testCount: number }> {
    const suite = await this.loadSuite(id, { computeIds: false });
    return {
      name: suite.name,
      description: suite.description,
      testCount: suite.tests.length
    };
  }

  /**
   * Compute deterministic IDs for all test cases in a suite
   * Uses system prompt and test content to generate unique identifiers
   */
  private computeTestIds(suite: TestSuite): LoadedTestCase[] {
    return suite.tests.map(test => {
      // Normalize answers for consistent ID generation
      const normalizedTest = {
        ...test,
        answers: normalizeAnswers(test.answers),
        negative_answers: test.negative_answers ? normalizeAnswers(test.negative_answers) : undefined
      };

      const signature = computeTestSignature(normalizedTest, suite.system_prompt);
      const id = signatureHash(signature);

      return {
        ...normalizedTest,
        id
      };
    });
  }

  /**
   * Load multiple test suites by their IDs
   * Returns a map of suite ID to loaded suite
   */
  async loadSuites(ids: string[], options: LoadOptions = {}): Promise<Map<string, LoadedSuite>> {
    const results = new Map<string, LoadedSuite>();
    const errors: string[] = [];

    await Promise.allSettled(
      ids.map(async (id) => {
        try {
          const suite = await this.loadSuite(id, options);
          results.set(id, suite);
        } catch (error) {
          errors.push(`Failed to load suite '${id}': ${(error as Error).message}`);
        }
      })
    );

    if (errors.length > 0) {
      this.logger?.warn(`Some test suites failed to load:\n${errors.join('\n')}`);
    }

    return results;
  }

  /**
   * Get the full file path for a test suite
   * Useful for debugging or external tools
   */
  getSuitePath(id: string): string {
    // Use the mapping if available, otherwise fallback to direct filename
    const filename = this.idToFilenameMap.get(id) || `${id}.json`;
    return path.resolve(this.baseDir, filename);
  }
}
