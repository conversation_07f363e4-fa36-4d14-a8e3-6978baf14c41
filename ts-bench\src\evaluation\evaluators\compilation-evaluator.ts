import * as ts from 'typescript';
import { readFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { Evaluator, EvaluationContext, EvaluationResult, EvaluationStrategy, CompilationConfig } from '../types';
import { findGeneratedFiles } from '../utils/file-utils';

/**
 * Compilation evaluator that checks if generated code compiles successfully
 */
export class CompilationEvaluator extends Evaluator {
  private config: CompilationConfig;

  constructor(config: CompilationConfig = {}) {
    super(EvaluationStrategy.COMPILATION);
    this.config = {
      compilerOptions: {
        target: ts.ScriptTarget.ES2020,
        module: ts.ModuleKind.CommonJS,
        strict: false,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        resolveJsonModule: true,
        allowSyntheticDefaultImports: true,
        jsx: ts.JsxEmit.React
      },
      target: 'ES2020',
      typeCheck: false,
      timeout: 30000,
      ...config
    };
  }

  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    const startTime = Date.now();
    
    try {
      const { workspacePath } = context;
      
      if (!existsSync(workspacePath)) {
        return {
          score: 0,
          success: false,
          error: `Workspace path does not exist: ${workspacePath}`,
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Find TypeScript/JavaScript files to compile
      const sourceFiles = await findGeneratedFiles(workspacePath, ['.ts', '.tsx', '.js', '.jsx']);
      
      if (sourceFiles.length === 0) {
        return {
          score: 0,
          success: false,
          error: 'No source files found to compile',
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Compile files
      const compilationResults = await this.compileFiles(workspacePath, sourceFiles);
      
      // Calculate score based on compilation results
      const score = this.calculateCompilationScore(compilationResults);
      
      return {
        score,
        success: score > 0.8, // Consider successful if most files compile without errors
        details: {
          compilationResults,
          sourceFiles,
          compilerOptions: this.config.compilerOptions
        },
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        score: 0,
        success: false,
        error: `Compilation evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Compile files using TypeScript compiler
   */
  private async compileFiles(workspacePath: string, sourceFiles: string[]): Promise<any[]> {
    const results = [];

    for (const file of sourceFiles) {
      const filePath = join(workspacePath, file);
      const result = await this.compileFile(filePath);
      results.push({ file, ...result });
    }

    return results;
  }

  /**
   * Compile a single file
   */
  private async compileFile(filePath: string): Promise<any> {
    try {
      const sourceCode = readFileSync(filePath, 'utf8');
      
      // Create a simple program with the file
      const compilerHost = ts.createCompilerHost(this.config.compilerOptions!);
      const program = ts.createProgram([filePath], this.config.compilerOptions!, compilerHost);
      
      // Get diagnostics
      const syntacticDiagnostics = program.getSyntacticDiagnostics();
      const semanticDiagnostics = this.config.typeCheck 
        ? program.getSemanticDiagnostics() 
        : [];
      
      const allDiagnostics = [...syntacticDiagnostics, ...semanticDiagnostics];
      
      // Categorize diagnostics
      const errors = allDiagnostics.filter(d => d.category === ts.DiagnosticCategory.Error);
      const warnings = allDiagnostics.filter(d => d.category === ts.DiagnosticCategory.Warning);
      
      // Try to emit (transpile) the code
      let emitResult;
      try {
        emitResult = program.emit();
      } catch (emitError) {
        emitResult = { emitSkipped: true };
      }

      return {
        success: errors.length === 0 && !emitResult.emitSkipped,
        errors: errors.map(this.formatDiagnostic),
        warnings: warnings.map(this.formatDiagnostic),
        emitSkipped: emitResult.emitSkipped,
        score: this.calculateFileScore(errors, warnings, emitResult.emitSkipped)
      };

    } catch (error) {
      return {
        success: false,
        errors: [{ message: error instanceof Error ? error.message : String(error) }],
        warnings: [],
        emitSkipped: true,
        score: 0
      };
    }
  }

  /**
   * Format TypeScript diagnostic for readable output
   */
  private formatDiagnostic(diagnostic: ts.Diagnostic): any {
    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
    
    if (diagnostic.file && diagnostic.start !== undefined) {
      const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
      return {
        file: diagnostic.file.fileName,
        line: line + 1,
        character: character + 1,
        message,
        code: diagnostic.code
      };
    }
    
    return {
      message,
      code: diagnostic.code
    };
  }

  /**
   * Calculate score for a single file compilation
   */
  private calculateFileScore(errors: any[], warnings: any[], emitSkipped: boolean): number {
    if (errors.length > 0) {
      return 0; // Any errors = 0 score
    }
    
    if (emitSkipped) {
      return 0.2; // Emit failed but no syntax errors
    }
    
    // Reduce score based on warnings
    const warningPenalty = Math.min(0.3, warnings.length * 0.1);
    return Math.max(0.5, 1.0 - warningPenalty);
  }

  /**
   * Calculate overall compilation score
   */
  private calculateCompilationScore(results: any[]): number {
    if (results.length === 0) {
      return 0;
    }

    const totalScore = results.reduce((sum, result) => sum + result.score, 0);
    return totalScore / results.length;
  }

  /**
   * Quick syntax check without full compilation
   */
  async quickSyntaxCheck(code: string, fileName: string = 'temp.ts'): Promise<boolean> {
    try {
      const sourceFile = ts.createSourceFile(
        fileName,
        code,
        ts.ScriptTarget.Latest,
        true
      );

      // Check for syntax errors
      const syntacticDiagnostics = sourceFile.parseDiagnostics;
      return syntacticDiagnostics.length === 0;

    } catch (error) {
      return false;
    }
  }

  /**
   * Check if code contains basic TypeScript/JavaScript constructs
   */
  private hasValidCodeStructure(code: string): boolean {
    // Basic checks for valid code structure
    const hasBalancedBraces = this.hasBalancedBraces(code);
    const hasValidSyntax = !code.includes('SyntaxError');
    
    return hasBalancedBraces && hasValidSyntax;
  }

  /**
   * Check for balanced braces in code
   */
  private hasBalancedBraces(code: string): boolean {
    let braceCount = 0;
    let parenCount = 0;
    let bracketCount = 0;
    
    for (const char of code) {
      switch (char) {
        case '{': braceCount++; break;
        case '}': braceCount--; break;
        case '(': parenCount++; break;
        case ')': parenCount--; break;
        case '[': bracketCount++; break;
        case ']': bracketCount--; break;
      }
    }
    
    return braceCount === 0 && parenCount === 0 && bracketCount === 0;
  }
}
