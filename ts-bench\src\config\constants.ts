export const TS_BENCH_CONTAINER = "ts-bench-container";
export const EXERCISM_PRACTICE_PATH = "exercism-typescript";
export const HEADER_INSTRUCTION = "Solve this TypeScript exercise. Read the test file to understand requirements and implement the solution.";
export const TOP_25_EXERCISES = 'acronym,anagram,bank-account,binary-search,binary-search-tree,bowling,complex-numbers,connect,crypto-square,diamond,dnd-character,flatten-array,food-chain,house,pascals-triangle,rational-numbers,react,rectangles,relative-distance,robot-name,spiral-matrix,transpose,two-bucket,variable-length-quantity,wordy';
