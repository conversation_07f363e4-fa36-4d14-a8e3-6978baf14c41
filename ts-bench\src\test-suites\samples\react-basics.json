{"name": "React Component Basics", "description": "Tests AI coding agents' ability to create basic React components with TypeScript", "system_prompt": "You are a skilled React developer. Create React components using TypeScript with proper typing. Follow React best practices including functional components, proper prop interfaces, and modern hooks. Export components as default exports. Use clear, descriptive component and prop names.", "tests": [{"prompt": "Create a React functional component called UserProfile that displays a user's name and email. The component should accept props with name (string) and email (string) properties.", "answers": ["function UserProfile", "interface Props", "name: string", "email: string", "export default UserProfile"], "negative_answers": ["class UserProfile", "React.Component", "any", "var UserProfile"]}, {"prompt": "Create a React component called Counter that displays a count starting at 0 and has a button to increment the count. Use the useState hook for state management.", "answers": ["function Counter", "useState", "const [count, setCount]", "setCount(count + 1)", "export default Counter"], "negative_answers": ["class Counter", "this.state", "this.setState", "var count"]}, {"prompt": "Create a React component called TodoItem that displays a todo item with a checkbox and text. The component should accept props with text (string), completed (boolean), and onToggle (function) properties.", "answers": ["function TodoItem", "interface Props", "text: string", "completed: boolean", "onToggle: () => void", "export default TodoItem"], "negative_answers": ["class TodoItem", "any", "onToggle: Function", "React.Component"]}]}