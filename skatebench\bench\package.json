{"name": "skatebench", "module": "index.ts", "type": "module", "scripts": {"start": "bun --bun run ./cli.tsx", "cli": "bun --bun run ./cli.tsx", "run": "bun --bun run ./index.ts", "typecheck": "tsc -p tsconfig.json --noEmit"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^22.7.8", "@types/react": "^18.3.5"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@ai-sdk/openai": "^2.0.0-beta.12", "@openrouter/ai-sdk-provider": "^1.0.0-beta.1", "ai": "^5.0.0-beta.19", "ink": "^4.4.1", "ink-select-input": "^6.0.1", "ink-text-input": "^6.1.0", "react": "^18.3.1"}}