import type { Agent<PERSON>esult, AgentType, ProviderType } from './types';

/**
 * Extended result type for test suite evaluation with test suite specific fields
 */
export interface TestSuiteAgentResult extends AgentResult {
    /** The test suite ID */
    suiteId: string;
    /** The specific test case ID within the suite */
    testCaseId: string;
    /** The original prompt/question from the test case */
    prompt: string;
    /** The expected answer for comparison */
    expectedAnswer?: string;
    /** Evaluation score or correctness measure */
    evaluationScore?: number;
    /** Additional evaluation metadata */
    evaluationDetails?: Record<string, any>;
}

/**
 * Context information for test suite execution
 */
export interface TestSuiteExecutionContext {
    /** The test suite ID */
    suiteId: string;
    /** The test case ID */
    testCaseId: string;
    /** The test case prompt */
    prompt: string;
    /** System prompt for the suite */
    systemPrompt?: string;
    /** Expected answer */
    expectedAnswer: string;
    /** Additional test case metadata */
    metadata?: Record<string, any>;
}

/**
 * Configuration options for test suite execution
 */
export interface TestSuiteRunOptions {
    /** Whether to use Docker for execution */
    useDocker?: boolean;
    /** Timeout for agent execution in milliseconds */
    timeout?: number;
    /** Whether to include detailed evaluation */
    includeEvaluation?: boolean;
    /** Custom evaluation parameters */
    evaluationParams?: Record<string, any>;
}

/**
 * Utility functions for working with test suite results
 */
export class TestSuiteResultUtils {
    /**
     * Converts a regular AgentResult to TestSuiteAgentResult
     */
    static toTestSuiteResult(
        agentResult: AgentResult,
        context: TestSuiteExecutionContext
    ): TestSuiteAgentResult {
        return {
            ...agentResult,
            suiteId: context.suiteId,
            testCaseId: context.testCaseId,
            prompt: context.prompt,
            expectedAnswer: context.expectedAnswer
        };
    }

    /**
     * Extracts the base AgentResult from a TestSuiteAgentResult
     */
    static toAgentResult(testSuiteResult: TestSuiteAgentResult): AgentResult {
        const { suiteId, testCaseId, prompt, expectedAnswer, evaluationScore, evaluationDetails, ...agentResult } = testSuiteResult;
        return agentResult;
    }

    /**
     * Type guard to check if a result is a TestSuiteAgentResult
     */
    static isTestSuiteResult(result: AgentResult | TestSuiteAgentResult): result is TestSuiteAgentResult {
        return 'suiteId' in result && 'testCaseId' in result && 'prompt' in result;
    }

    /**
     * Extracts suite and test case IDs from exercise name
     */
    static parseExerciseName(exerciseName: string): { suiteId: string; testCaseId: string } | null {
        const parts = exerciseName.split(':');
        if (parts.length === 2) {
            return {
                suiteId: parts[0],
                testCaseId: parts[1]
            };
        }
        return null;
    }

    /**
     * Creates exercise name from suite and test case IDs
     */
    static createExerciseName(suiteId: string, testCaseId: string): string {
        return `${suiteId}:${testCaseId}`;
    }
}

/**
 * Result from executing a single test case in a test suite (skatebench compatible)
 */
export interface TestSuiteResult {
    suiteId: string;
    testCaseId: string;
    prompt: string;
    model: string;
    agent: AgentType;
    provider: ProviderType;
    success: boolean;
    correct: boolean;
    error?: string;
    duration: number;
    cost?: number;
    agentOutput?: string;
    expectedOutput?: string;
    evaluationDetails?: any;
    workspacePath?: string;
    timestamp: Date;
}

/**
 * Result from executing a single test case with detailed breakdown
 */
export interface TestSuiteExecutionResult {
    suiteId: string;
    testCaseId: string;
    agentResult: {
        success: boolean;
        output?: string;
        error?: string;
        duration: number;
        workspacePath?: string;
    };
    evaluationResult: {
        correct: boolean;
        score?: number;
        error?: string;
        duration: number;
        details?: any;
    };
    totalDuration: number;
    cost?: number;
}

/**
 * Model performance result compatible with skatebench format
 */
export interface ModelPerformanceResult {
    model: string;
    agent: AgentType;
    provider: ProviderType;
    correct: number;
    incorrect: number;
    errors: number;
    totalTests: number;
    successRate: number; // percentage
    errorRate: number; // percentage
    averageDuration: number; // milliseconds
    totalCost: number;
    averageCostPerTest: number;
    testResults: TestSuiteResult[];
}

/**
 * Overall benchmark result with model rankings
 */
export interface TestSuiteBenchmarkResult {
    rankings: ModelPerformanceResult[];
    metadata: {
        totalTestCases: number;
        totalSuites: number;
        executionDate: Date;
        totalDuration: number;
        totalCost: number;
        suiteIds: string[];
    };
    rawResults: TestSuiteResult[];
}

/**
 * Metrics for aggregating test suite performance
 */
export interface TestSuiteMetrics {
    totalTests: number;
    correctTests: number;
    incorrectTests: number;
    errorTests: number;
    totalDuration: number;
    totalCost: number;
    averageDuration: number;
    averageCost: number;
    successRate: number;
    errorRate: number;
}

/**
 * Type guard functions for distinguishing between exercise and test suite results
 */
export class TestSuiteTypeGuards {
    /**
     * Checks if an exercise name represents a test suite
     */
    static isTestSuiteExercise(exerciseName: string): boolean {
        return exerciseName.includes(':') && exerciseName.split(':').length === 2;
    }

    /**
     * Checks if a result comes from test suite execution
     */
    static isFromTestSuite(result: AgentResult): boolean {
        return this.isTestSuiteExercise(result.exercise);
    }

    /**
     * Type guard to check if result is from test suite execution
     */
    static isTestSuiteResult(result: any): result is TestSuiteResult {
        return result &&
               typeof result.suiteId === 'string' &&
               typeof result.testCaseId === 'string' &&
               typeof result.prompt === 'string' &&
               typeof result.correct === 'boolean';
    }

    /**
     * Type guard to check if result is from exercise execution
     */
    static isExerciseResult(result: any): result is any {
        return result &&
               typeof result.exercise === 'string' &&
               result.suiteId === undefined &&
               result.testCaseId === undefined;
    }
}

/**
 * Convert TestSuiteResult to ModelPerformanceResult format
 */
export function aggregateResultsByModel(results: TestSuiteResult[]): Map<string, ModelPerformanceResult> {
    const modelMap = new Map<string, ModelPerformanceResult>();

    for (const result of results) {
        const modelKey = `${result.model}-${result.agent}-${result.provider}`;

        if (!modelMap.has(modelKey)) {
            modelMap.set(modelKey, {
                model: result.model,
                agent: result.agent,
                provider: result.provider,
                correct: 0,
                incorrect: 0,
                errors: 0,
                totalTests: 0,
                successRate: 0,
                errorRate: 0,
                averageDuration: 0,
                totalCost: 0,
                averageCostPerTest: 0,
                testResults: []
            });
        }

        const modelResult = modelMap.get(modelKey)!;
        modelResult.testResults.push(result);
        modelResult.totalTests++;

        if (result.error) {
            modelResult.errors++;
        } else if (result.correct) {
            modelResult.correct++;
        } else {
            modelResult.incorrect++;
        }

        modelResult.totalCost += result.cost || 0;
    }

    // Calculate derived metrics
    for (const [, modelResult] of modelMap) {
        modelResult.successRate = (modelResult.correct / modelResult.totalTests) * 100;
        modelResult.errorRate = (modelResult.errors / modelResult.totalTests) * 100;
        modelResult.averageDuration = modelResult.testResults.reduce((sum, r) => sum + r.duration, 0) / modelResult.totalTests;
        modelResult.averageCostPerTest = modelResult.totalCost / modelResult.totalTests;
    }

    return modelMap;
}

/**
 * Calculate metrics from test suite results
 */
export function calculateTestSuiteMetrics(results: TestSuiteResult[]): TestSuiteMetrics {
    const totalTests = results.length;
    const correctTests = results.filter(r => r.correct && !r.error).length;
    const errorTests = results.filter(r => r.error).length;
    const incorrectTests = totalTests - correctTests - errorTests;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);

    return {
        totalTests,
        correctTests,
        incorrectTests,
        errorTests,
        totalDuration,
        totalCost,
        averageDuration: totalDuration / totalTests,
        averageCost: totalCost / totalTests,
        successRate: (correctTests / totalTests) * 100,
        errorRate: (errorTests / totalTests) * 100
    };
}
