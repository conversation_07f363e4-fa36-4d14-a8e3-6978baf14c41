{"rankings": [{"model": "gpt-5-for-real", "correct": 207, "incorrect": 3, "errors": 0, "totalTests": 210, "successRate": 98.57142857142858, "errorRate": 0, "averageDuration": 18594, "totalCost": 0.06964487500000001, "averageCostPerTest": 0.0003316422619047619}, {"model": "o3-pro", "correct": 197, "incorrect": 13, "errors": 0, "totalTests": 210, "successRate": 93.80952380952381, "errorRate": 0, "averageDuration": 0, "totalCost": 0.447782, "averageCostPerTest": 0.002132295238095238}, {"model": "o3", "correct": 183, "incorrect": 27, "errors": 0, "totalTests": 210, "successRate": 87.14285714285714, "errorRate": 0, "averageDuration": 0, "totalCost": 0.04874739999999998, "averageCostPerTest": 0.00023213047619047612}, {"model": "grok-4", "correct": 166, "incorrect": 44, "errors": 0, "totalTests": 210, "successRate": 79.04761904761905, "errorRate": 0, "averageDuration": 0, "totalCost": 4.820753250000001, "averageCostPerTest": 0.02295596785714286}, {"model": "gemini-2.5-pro", "correct": 116, "incorrect": 94, "errors": 0, "totalTests": 210, "successRate": 55.23809523809524, "errorRate": 0, "averageDuration": 0, "totalCost": 1.9753449999999997, "averageCostPerTest": 0.00940640476190476}, {"model": "deepseek-r1-0528", "correct": 116, "incorrect": 94, "errors": 0, "totalTests": 210, "successRate": 55.23809523809524, "errorRate": 0, "averageDuration": 30192, "totalCost": 0.4227918346920001, "averageCostPerTest": 0.002013294450914286}, {"model": "deepseek-v3.1-thinking", "correct": 114, "incorrect": 96, "errors": 0, "totalTests": 210, "successRate": 54.285714285714285, "errorRate": 0, "averageDuration": 27361, "totalCost": 0.30041727999999995, "averageCostPerTest": 0.001430558476190476}, {"model": "gpt-5-mini", "correct": 108, "incorrect": 102, "errors": 0, "totalTests": 210, "successRate": 51.42857142857142, "errorRate": 0, "averageDuration": 12072, "totalCost": 0.015555274999999999, "averageCostPerTest": 7.407273809523809e-05}, {"model": "sonoma-sky-alpha", "correct": 96, "incorrect": 114, "errors": 0, "totalTests": 210, "successRate": 45.714285714285715, "errorRate": 0, "averageDuration": 7098, "totalCost": 0, "averageCostPerTest": 0}, {"model": "claude-4-opus", "correct": 95, "incorrect": 115, "errors": 0, "totalTests": 210, "successRate": 45.23809523809524, "errorRate": 0, "averageDuration": 0, "totalCost": 0.19064025000000004, "averageCostPerTest": 0.0009078107142857144}, {"model": "deepseek-v3.1", "correct": 95, "incorrect": 115, "errors": 0, "totalTests": 210, "successRate": 45.23809523809524, "errorRate": 0, "averageDuration": 23038, "totalCost": 0.2725596000000002, "averageCostPerTest": 0.001297902857142858}, {"model": "o4-mini", "correct": 84, "incorrect": 126, "errors": 0, "totalTests": 210, "successRate": 40, "errorRate": 0, "averageDuration": 0, "totalCost": 0.037842970000000024, "averageCostPerTest": 0.00018020461904761915}, {"model": "gemini-2.5-flash", "correct": 78, "incorrect": 132, "errors": 0, "totalTests": 210, "successRate": 37.142857142857146, "errorRate": 0, "averageDuration": 0, "totalCost": 0.48156749999999976, "averageCostPerTest": 0.00229317857142857}, {"model": "grok-3-mini", "correct": 62, "incorrect": 148, "errors": 0, "totalTests": 210, "successRate": 29.523809523809526, "errorRate": 0, "averageDuration": 0, "totalCost": 0.48757987500000005, "averageCostPerTest": 0.002321808928571429}, {"model": "glm-4.5v", "correct": 55, "incorrect": 155, "errors": 0, "totalTests": 210, "successRate": 26.190476190476193, "errorRate": 0, "averageDuration": 11926, "totalCost": 0.34167650999999977, "averageCostPerTest": 0.0016270309999999988}, {"model": "glm-4.5", "correct": 39, "incorrect": 171, "errors": 0, "totalTests": 210, "successRate": 18.571428571428573, "errorRate": 0, "averageDuration": 0, "totalCost": 0.15719140000000006, "averageCostPerTest": 0.0007485304761904764}, {"model": "qwen3-235b-a22b-thinking", "correct": 37, "incorrect": 173, "errors": 0, "totalTests": 210, "successRate": 17.61904761904762, "errorRate": 0, "averageDuration": 0, "totalCost": 0.16003554379999993, "averageCostPerTest": 0.0007620740180952377}, {"model": "gpt-5-nano", "correct": 32, "incorrect": 178, "errors": 0, "totalTests": 210, "successRate": 15.238095238095239, "errorRate": 0, "averageDuration": 24939, "totalCost": 0.012003335, "averageCostPerTest": 5.71587380952381e-05}, {"model": "gpt-4o", "correct": 24, "incorrect": 186, "errors": 0, "totalTests": 210, "successRate": 11.428571428571429, "errorRate": 0, "averageDuration": 0, "totalCost": 0.003543499999999999, "averageCostPerTest": 1.6873809523809517e-05}, {"model": "claude-4-sonnet", "correct": 20, "incorrect": 190, "errors": 0, "totalTests": 210, "successRate": 9.523809523809524, "errorRate": 0, "averageDuration": 0, "totalCost": 0.038606999999999995, "averageCostPerTest": 0.00018384285714285713}, {"model": "sonoma-dusk-alpha", "correct": 19, "incorrect": 191, "errors": 0, "totalTests": 210, "successRate": 9.047619047619047, "errorRate": 0, "averageDuration": 589, "totalCost": 0, "averageCostPerTest": 0}, {"model": "gpt-4.1", "correct": 13, "incorrect": 197, "errors": 0, "totalTests": 210, "successRate": 6.190476190476191, "errorRate": 0, "averageDuration": 0, "totalCost": 0.0024768000000000004, "averageCostPerTest": 1.1794285714285716e-05}, {"model": "kimi-k2", "correct": 0, "incorrect": 210, "errors": 0, "totalTests": 210, "successRate": 0, "errorRate": 0, "averageDuration": 0, "totalCost": 0.005207159999999985, "averageCostPerTest": 2.479599999999993e-05}, {"model": "qwen-3-32b", "correct": 0, "incorrect": 210, "errors": 0, "totalTests": 210, "successRate": 0, "errorRate": 0, "averageDuration": 0, "totalCost": 0.020277391999999984, "averageCostPerTest": 9.655900952380944e-05}], "metadata": {"timestamp": "2025-09-06T01:15:19.680Z", "totalModels": 24, "totalTestsRun": 5040, "overallCorrect": 1956, "overallIncorrect": 3084, "overallErrors": 0, "overallSuccessRate": 38.80952380952381, "overallErrorRate": 0, "totalCost": 10.312245750491947, "averageCostPerTest": 0.0020460805060499893, "config": {"maxConcurrency": 30, "testRunsPerModel": 30, "timeoutSeconds": 400}, "testSuite": "Technical Trick Terminology", "suiteId": "skate-trick-test", "version": "2025-07-28"}}