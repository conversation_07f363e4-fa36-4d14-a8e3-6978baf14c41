/**
 * TypeScript interfaces for the test suite system adapted from skatebench
 * for coding agent evaluation tasks.
 */

/**
 * Individual test case within a test suite
 */
export interface TestCase {
  /** The prompt/question presented to the coding agent */
  prompt: string;
  
  /** Array of expected correct answers or patterns */
  answers: string[];
  
  /** Optional array of incorrect answers that should be rejected */
  negative_answers?: string[];
}

/**
 * Complete test suite definition loaded from JSON
 */
export interface TestSuite {
  /** Human-readable name of the test suite */
  name: string;
  
  /** Detailed description of what this test suite evaluates */
  description: string;
  
  /** System prompt that provides context and instructions to the agent */
  system_prompt: string;
  
  /** Array of test cases in this suite */
  tests: TestCase[];
}

/**
 * Test suite with computed identifiers for tracking and results
 */
export interface LoadedSuite extends TestSuite {
  /** Unique identifier for this test suite (derived from filename) */
  id: string;
  
  /** Array of test cases with computed IDs */
  tests: LoadedTestCase[];
}

/**
 * Test case with computed identifier
 */
export interface LoadedTestCase extends TestCase {
  /** Unique identifier for this test case */
  id: string;
}

/**
 * Metadata for test suite organization and filtering
 */
export interface TestSuiteMetadata {
  /** Version of the test suite format */
  version?: string;
  
  /** Tags for categorizing test suites */
  tags?: string[];
  
  /** Difficulty level (beginner, intermediate, advanced) */
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  
  /** Estimated time to complete in minutes */
  estimated_time?: number;
  
  /** Programming language or framework focus */
  language?: string;
  
  /** Author information */
  author?: string;
  
  /** Creation or last modified date */
  created?: string;
}

/**
 * Extended test suite with metadata
 */
export interface TestSuiteWithMetadata extends TestSuite {
  /** Additional metadata for the test suite */
  metadata?: TestSuiteMetadata;
}

/**
 * Result of test suite validation
 */
export interface ValidationResult {
  /** Whether the test suite is valid */
  valid: boolean;
  
  /** Array of validation error messages */
  errors: string[];
  
  /** Array of validation warning messages */
  warnings: string[];
}

/**
 * Options for loading test suites
 */
export interface LoadOptions {
  /** Whether to validate the test suite structure */
  validate?: boolean;
  
  /** Whether to compute test IDs */
  computeIds?: boolean;
  
  /** Base directory for resolving relative paths */
  baseDir?: string;
}
