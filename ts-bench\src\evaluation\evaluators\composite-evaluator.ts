import { 
  Eva<PERSON>ator, 
  EvaluationContext, 
  EvaluationResult, 
  EvaluationStrategy, 
  EvaluationConfig,
  EvaluationMetrics 
} from '../types';
import { PatternEvaluator } from './pattern-evaluator';
import { FileEvaluator } from './file-evaluator';
import { CompilationEvaluator } from './compilation-evaluator';
import { FunctionalityEvaluator } from './functionality-evaluator';

/**
 * Composite evaluator that combines multiple evaluation strategies
 */
export class CompositeEvaluator extends Evaluator {
  private evaluators: Map<EvaluationStrategy, Evaluator>;
  private config: EvaluationConfig;

  constructor(config: EvaluationConfig) {
    super(EvaluationStrategy.COMPOSITE);
    this.config = {
      strategies: [EvaluationStrategy.PATTERN],
      weights: {
        [EvaluationStrategy.PATTERN]: 0.4,
        [EvaluationStrategy.FILE]: 0.3,
        [EvaluationStrategy.COMPILATION]: 0.2,
        [EvaluationStrategy.FUNCTIONALITY]: 0.1,
        [EvaluationStrategy.COMPOSITE]: 1.0
      },
      timeout: 60000,
      failFast: false,
      ...config
    };

    this.evaluators = new Map();
    this.initializeEvaluators();
  }

  /**
   * Initialize evaluators based on configuration
   */
  private initializeEvaluators(): void {
    for (const strategy of this.config.strategies) {
      const strategyConfig = this.config.strategyConfig?.[strategy] || {};
      
      switch (strategy) {
        case EvaluationStrategy.PATTERN:
          this.evaluators.set(strategy, new PatternEvaluator(strategyConfig));
          break;
        case EvaluationStrategy.FILE:
          this.evaluators.set(strategy, new FileEvaluator(strategyConfig));
          break;
        case EvaluationStrategy.COMPILATION:
          this.evaluators.set(strategy, new CompilationEvaluator(strategyConfig));
          break;
        case EvaluationStrategy.FUNCTIONALITY:
          this.evaluators.set(strategy, new FunctionalityEvaluator(strategyConfig));
          break;
      }
    }
  }

  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    const startTime = Date.now();
    const results: Record<EvaluationStrategy, EvaluationResult> = {};
    const errors: string[] = [];

    try {
      // Run evaluations based on strategy
      if (this.config.failFast) {
        await this.runSequentialEvaluation(context, results, errors);
      } else {
        await this.runParallelEvaluation(context, results, errors);
      }

      // Aggregate results
      const aggregatedResult = this.aggregateResults(results, errors);
      aggregatedResult.executionTime = Date.now() - startTime;

      return aggregatedResult;

    } catch (error) {
      return {
        score: 0,
        success: false,
        error: `Composite evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Run evaluations sequentially with fail-fast behavior
   */
  private async runSequentialEvaluation(
    context: EvaluationContext,
    results: Record<EvaluationStrategy, EvaluationResult>,
    errors: string[]
  ): Promise<void> {
    for (const strategy of this.config.strategies) {
      const evaluator = this.evaluators.get(strategy);
      if (!evaluator) continue;

      try {
        const result = await this.runWithTimeout(
          evaluator.evaluate(context),
          this.config.timeout || 60000
        );
        
        results[strategy] = result;

        // Check if this is a critical failure
        if (this.isCriticalFailure(strategy, result)) {
          errors.push(`Critical failure in ${strategy}: ${result.error || 'Unknown error'}`);
          break;
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`${strategy} evaluation failed: ${errorMessage}`);
        
        results[strategy] = {
          score: 0,
          success: false,
          error: errorMessage,
          strategy
        };

        if (this.isCriticalStrategy(strategy)) {
          break;
        }
      }
    }
  }

  /**
   * Run evaluations in parallel
   */
  private async runParallelEvaluation(
    context: EvaluationContext,
    results: Record<EvaluationStrategy, EvaluationResult>,
    errors: string[]
  ): Promise<void> {
    const evaluationPromises = this.config.strategies.map(async (strategy) => {
      const evaluator = this.evaluators.get(strategy);
      if (!evaluator) return;

      try {
        const result = await this.runWithTimeout(
          evaluator.evaluate(context),
          this.config.timeout || 60000
        );
        
        results[strategy] = result;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`${strategy} evaluation failed: ${errorMessage}`);
        
        results[strategy] = {
          score: 0,
          success: false,
          error: errorMessage,
          strategy
        };
      }
    });

    await Promise.all(evaluationPromises);
  }

  /**
   * Run evaluation with timeout
   */
  private async runWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Evaluation timeout')), timeout);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Check if a failure is critical
   */
  private isCriticalFailure(strategy: EvaluationStrategy, result: EvaluationResult): boolean {
    // Compilation errors are often critical
    if (strategy === EvaluationStrategy.COMPILATION && result.score === 0) {
      return true;
    }

    // Pattern matching failures might be critical depending on configuration
    if (strategy === EvaluationStrategy.PATTERN && result.score === 0) {
      const patternConfig = this.config.strategyConfig?.[EvaluationStrategy.PATTERN];
      return patternConfig?.critical === true;
    }

    return false;
  }

  /**
   * Check if a strategy is critical
   */
  private isCriticalStrategy(strategy: EvaluationStrategy): boolean {
    const strategyConfig = this.config.strategyConfig?.[strategy];
    return strategyConfig?.critical === true;
  }

  /**
   * Aggregate results from multiple evaluators
   */
  private aggregateResults(
    results: Record<EvaluationStrategy, EvaluationResult>,
    errors: string[]
  ): EvaluationResult {
    const strategyResults = Object.values(results);
    
    if (strategyResults.length === 0) {
      return {
        score: 0,
        success: false,
        error: 'No evaluation results available',
        strategy: this.strategy
      };
    }

    // Calculate weighted score
    let totalWeight = 0;
    let weightedScore = 0;
    let successCount = 0;

    for (const [strategy, result] of Object.entries(results)) {
      const weight = this.config.weights?.[strategy as EvaluationStrategy] || 1.0;
      totalWeight += weight;
      weightedScore += result.score * weight;
      
      if (result.success) {
        successCount++;
      }
    }

    const finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0;
    const overallSuccess = this.determineOverallSuccess(results, finalScore);

    // Create metrics
    const metrics: EvaluationMetrics = {
      strategyResults: results,
      overallScore: finalScore,
      totalTime: strategyResults.reduce((sum, r) => sum + (r.executionTime || 0), 0),
      successCount,
      failureCount: strategyResults.length - successCount
    };

    return {
      score: finalScore,
      success: overallSuccess,
      details: {
        metrics,
        individualResults: results,
        errors: errors.length > 0 ? errors : undefined
      },
      strategy: this.strategy,
      error: errors.length > 0 ? errors.join('; ') : undefined
    };
  }

  /**
   * Determine overall success based on individual results
   */
  private determineOverallSuccess(
    results: Record<EvaluationStrategy, EvaluationResult>,
    finalScore: number
  ): boolean {
    // Check if any critical strategies failed
    for (const [strategy, result] of Object.entries(results)) {
      if (this.isCriticalStrategy(strategy as EvaluationStrategy) && !result.success) {
        return false;
      }
    }

    // Use score-based success determination
    return finalScore > 0.6; // Configurable threshold
  }

  /**
   * Get evaluation metrics
   */
  getMetrics(result: EvaluationResult): EvaluationMetrics | undefined {
    return result.details?.metrics;
  }

  /**
   * Add a custom evaluator
   */
  addEvaluator(strategy: EvaluationStrategy, evaluator: Evaluator): void {
    this.evaluators.set(strategy, evaluator);
    
    if (!this.config.strategies.includes(strategy)) {
      this.config.strategies.push(strategy);
    }
  }

  /**
   * Remove an evaluator
   */
  removeEvaluator(strategy: EvaluationStrategy): void {
    this.evaluators.delete(strategy);
    this.config.strategies = this.config.strategies.filter(s => s !== strategy);
  }

  /**
   * Update strategy weight
   */
  updateWeight(strategy: EvaluationStrategy, weight: number): void {
    if (!this.config.weights) {
      this.config.weights = {};
    }
    this.config.weights[strategy] = weight;
  }
}
