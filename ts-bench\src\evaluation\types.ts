/**
 * Core types and interfaces for the evaluation system
 */

/**
 * Result of an evaluation operation
 */
export interface EvaluationResult {
  /** Overall score (0-1, where 1 is perfect) */
  score: number;
  /** Whether the evaluation was successful */
  success: boolean;
  /** Detailed information about the evaluation */
  details?: any;
  /** Error information if evaluation failed */
  error?: string;
  /** Strategy used for this evaluation */
  strategy: EvaluationStrategy;
  /** Execution time in milliseconds */
  executionTime?: number;
}

/**
 * Available evaluation strategies
 */
export enum EvaluationStrategy {
  PATTERN = 'pattern',
  FILE = 'file',
  COMPILATION = 'compilation',
  FUNCTIONALITY = 'functionality',
  COMPOSITE = 'composite'
}

/**
 * Context information for evaluation
 */
export interface EvaluationContext {
  /** The test case being evaluated */
  testCase: any;
  /** Agent's output/response */
  agentOutput: string;
  /** Workspace directory path */
  workspacePath: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Configuration for evaluation behavior
 */
export interface EvaluationConfig {
  /** Strategies to use for evaluation */
  strategies: EvaluationStrategy[];
  /** Weights for combining multiple strategies */
  weights?: Record<EvaluationStrategy, number>;
  /** Timeout for evaluation in milliseconds */
  timeout?: number;
  /** Whether to stop on first failure */
  failFast?: boolean;
  /** Additional configuration per strategy */
  strategyConfig?: Record<EvaluationStrategy, any>;
}

/**
 * Detailed metrics from evaluation
 */
export interface EvaluationMetrics {
  /** Individual strategy results */
  strategyResults: Record<EvaluationStrategy, EvaluationResult>;
  /** Overall combined score */
  overallScore: number;
  /** Total execution time */
  totalTime: number;
  /** Number of successful strategies */
  successCount: number;
  /** Number of failed strategies */
  failureCount: number;
}

/**
 * Criteria for determining evaluation success
 */
export interface EvaluationCriteria {
  /** Minimum score required for success */
  minScore?: number;
  /** Required strategies that must pass */
  requiredStrategies?: EvaluationStrategy[];
  /** Custom success function */
  customSuccess?: (result: EvaluationResult) => boolean;
}

/**
 * Abstract base class for all evaluators
 */
export abstract class Evaluator {
  protected strategy: EvaluationStrategy;

  constructor(strategy: EvaluationStrategy) {
    this.strategy = strategy;
  }

  /**
   * Evaluate the given context
   */
  abstract evaluate(context: EvaluationContext): Promise<EvaluationResult>;

  /**
   * Get the strategy this evaluator implements
   */
  getStrategy(): EvaluationStrategy {
    return this.strategy;
  }
}

/**
 * Pattern matching modes
 */
export enum MatchMode {
  CONTAINS = 'contains',
  EXACT = 'exact',
  REGEX = 'regex',
  CASE_INSENSITIVE = 'case_insensitive'
}

/**
 * Pattern match result
 */
export interface PatternMatch {
  /** The pattern that was matched */
  pattern: string;
  /** Whether the match was successful */
  matched: boolean;
  /** Match details (position, captured groups, etc.) */
  details?: any;
}

/**
 * File structure validation requirements
 */
export interface FileStructure {
  /** Required files */
  requiredFiles: string[];
  /** Optional files */
  optionalFiles?: string[];
  /** Required directories */
  requiredDirectories?: string[];
  /** File naming patterns */
  namingPatterns?: Record<string, RegExp>;
}

/**
 * Compilation configuration
 */
export interface CompilationConfig {
  /** TypeScript compiler options */
  compilerOptions?: any;
  /** Target ES version */
  target?: string;
  /** Whether to perform type checking */
  typeCheck?: boolean;
  /** Timeout for compilation */
  timeout?: number;
}

/**
 * Functionality test scenario
 */
export interface TestScenario {
  /** Name of the test scenario */
  name: string;
  /** Test function to execute */
  test: (workspace: string) => Promise<boolean>;
  /** Timeout for the test */
  timeout?: number;
  /** Whether this test is required */
  required?: boolean;
}
