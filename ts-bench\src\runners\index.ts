// Existing exercise-based runners
export { Agent<PERSON>unner } from './agent';
export { TestRunner } from './test';
export { ExerciseRunner } from './exercise';
export { TestOnlyRunner } from './test-only';

// New test suite runners
export { TestSuiteAgentRunner } from './test-suite-agent';
export { TestSuiteTestRunner } from './test-suite-test';
export { TestSuiteRunner } from './test-suite';

// Adapter classes for bridging exercise and test suite systems
export { 
    TestSuiteToExerciseAdapter, 
    TestSuiteExerciseReader, 
    TestSuiteAdapterUtils 
} from '../adapters/test-suite-adapter';

// Utility functions for working with both exercise and test suite formats
export { TestSuiteResultUtils, TestSuiteTypeGuards } from '../config/test-suite-types';

/**
 * Runner Types Guide:
 * 
 * Exercise-based runners (for Exercism-style exercises):
 * - AgentRunner: Executes agents on file-based exercises with instructions.md
 * - TestRunner: Runs tests against agent output for file-based exercises
 * - ExerciseRunner: Combined agent + test execution for exercises
 * - TestOnlyRunner: Test-only execution for exercises
 * 
 * Test suite runners (for prompt-based evaluation):
 * - TestSuiteAgentRunner: Executes agents on test suite prompts
 * - TestSuiteTestRunner: Evaluates agent output against expected answers
 * - TestSuiteRunner: Orchestrates test suite execution (agent + evaluation)
 * 
 * Adapter classes (for compatibility):
 * - TestSuiteToExerciseAdapter: Converts test suite data to exercise format
 * - TestSuiteExerciseReader: ExerciseReader implementation for test suites
 * - TestSuiteAdapterUtils: Utility functions for format conversion
 */
