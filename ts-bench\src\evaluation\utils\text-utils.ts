import { MatchMode, PatternMatch } from '../types';

/**
 * Normalize text for consistent comparison
 */
export function normalizeText(text: string, preservePunctuation: boolean = false): string {
  let normalized = text
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/\n+/g, ' '); // Replace newlines with spaces

  if (!preservePunctuation) {
    normalized = normalized.replace(/[^\w\s]/g, ''); // Remove punctuation
  }

  return normalized;
}

/**
 * Extract code blocks from markdown or mixed content
 */
export function extractCodeBlocks(text: string): string[] {
  const codeBlocks: string[] = [];
  
  // Match fenced code blocks (```language\ncode\n```)
  const fencedRegex = /```[\w]*\n?([\s\S]*?)\n?```/g;
  let match;
  
  while ((match = fencedRegex.exec(text)) !== null) {
    if (match[1]) {
      codeBlocks.push(match[1].trim());
    }
  }
  
  // Match inline code blocks (`code`)
  const inlineRegex = /`([^`]+)`/g;
  while ((match = inlineRegex.exec(text)) !== null) {
    if (match[1] && match[1].length > 10) { // Only longer inline code
      codeBlocks.push(match[1].trim());
    }
  }
  
  return codeBlocks;
}

/**
 * Detect programming language from code content
 */
export function detectLanguage(code: string): string {
  const patterns = {
    typescript: [/interface\s+\w+/, /type\s+\w+\s*=/, /import.*from/, /export.*{/],
    javascript: [/function\s+\w+/, /const\s+\w+\s*=/, /var\s+\w+/, /let\s+\w+/],
    react: [/import.*React/, /jsx/, /<\w+.*>/, /useState/, /useEffect/],
    html: [/<html/, /<head/, /<body/, /<div/, /<span/],
    css: [/\{[^}]*\}/, /\.[a-zA-Z][\w-]*\s*\{/, /#[a-zA-Z][\w-]*\s*\{/],
    json: [/^\s*\{/, /^\s*\[/, /"[^"]*"\s*:/],
    python: [/def\s+\w+/, /import\s+\w+/, /from\s+\w+\s+import/, /class\s+\w+/],
    java: [/public\s+class/, /private\s+\w+/, /public\s+static\s+void\s+main/]
  };

  for (const [language, regexes] of Object.entries(patterns)) {
    if (regexes.some(regex => regex.test(code))) {
      return language;
    }
  }

  return 'unknown';
}

/**
 * Sanitize code by removing comments and extra whitespace
 */
export function sanitizeCode(code: string): string {
  return code
    // Remove single-line comments
    .replace(/\/\/.*$/gm, '')
    // Remove multi-line comments
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // Remove extra whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Find import statements in code
 */
export function findImports(code: string): string[] {
  const imports: string[] = [];
  
  // ES6 imports
  const es6ImportRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
  let match;
  
  while ((match = es6ImportRegex.exec(code)) !== null) {
    imports.push(match[1]);
  }
  
  // CommonJS requires
  const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
  while ((match = requireRegex.exec(code)) !== null) {
    imports.push(match[1]);
  }
  
  return imports;
}

/**
 * Find export statements in code
 */
export function findExports(code: string): string[] {
  const exports: string[] = [];
  
  // Named exports
  const namedExportRegex = /export\s+(?:const|let|var|function|class)\s+(\w+)/g;
  let match;
  
  while ((match = namedExportRegex.exec(code)) !== null) {
    exports.push(match[1]);
  }
  
  // Export lists
  const exportListRegex = /export\s*\{\s*([^}]+)\s*\}/g;
  while ((match = exportListRegex.exec(code)) !== null) {
    const exportNames = match[1].split(',').map(name => name.trim().split(' as ')[0]);
    exports.push(...exportNames);
  }
  
  // Default exports
  if (code.includes('export default')) {
    exports.push('default');
  }
  
  return exports;
}

/**
 * Flexible pattern matching function
 */
export function matchPattern(text: string, pattern: string, mode: MatchMode): PatternMatch {
  try {
    switch (mode) {
      case MatchMode.EXACT:
        return {
          pattern,
          matched: text === pattern,
          details: { mode: 'exact' }
        };
        
      case MatchMode.CONTAINS:
        return {
          pattern,
          matched: text.includes(pattern),
          details: { mode: 'contains' }
        };
        
      case MatchMode.CASE_INSENSITIVE:
        return {
          pattern,
          matched: text.toLowerCase().includes(pattern.toLowerCase()),
          details: { mode: 'case_insensitive' }
        };
        
      case MatchMode.REGEX:
        const regex = new RegExp(pattern, 'i');
        const match = regex.exec(text);
        return {
          pattern,
          matched: match !== null,
          details: { 
            mode: 'regex',
            match: match ? {
              index: match.index,
              groups: match.slice(1)
            } : null
          }
        };
        
      default:
        throw new Error(`Unsupported match mode: ${mode}`);
    }
  } catch (error) {
    return {
      pattern,
      matched: false,
      details: { 
        error: error instanceof Error ? error.message : String(error),
        mode 
      }
    };
  }
}

/**
 * Score pattern matches
 */
export function scorePatternMatch(matches: PatternMatch[]): number {
  if (matches.length === 0) {
    return 0;
  }
  
  const successfulMatches = matches.filter(match => match.matched);
  return successfulMatches.length / matches.length;
}

/**
 * Extract key terms from text for analysis
 */
export function extractKeyTerms(text: string): string[] {
  const normalized = normalizeText(text);
  const words = normalized.split(' ');
  
  // Filter out common words and short words
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
  ]);
  
  return words
    .filter(word => word.length > 2 && !stopWords.has(word))
    .filter((word, index, array) => array.indexOf(word) === index); // Remove duplicates
}

/**
 * Calculate text similarity using simple word overlap
 */
export function calculateTextSimilarity(text1: string, text2: string): number {
  const terms1 = new Set(extractKeyTerms(text1));
  const terms2 = new Set(extractKeyTerms(text2));
  
  const intersection = new Set([...terms1].filter(term => terms2.has(term)));
  const union = new Set([...terms1, ...terms2]);
  
  return union.size > 0 ? intersection.size / union.size : 0;
}

/**
 * Check if text contains code-like patterns
 */
export function containsCode(text: string): boolean {
  const codePatterns = [
    /\{[^}]*\}/, // Braces
    /\([^)]*\)/, // Parentheses with content
    /\w+\.\w+/, // Method calls
    /import\s+/, // Import statements
    /function\s+/, // Function declarations
    /const\s+\w+\s*=/, // Variable declarations
    /\/\*[\s\S]*?\*\//, // Multi-line comments
    /\/\/.*$/, // Single-line comments
    /<\w+[^>]*>/ // HTML/JSX tags
  ];
  
  return codePatterns.some(pattern => pattern.test(text));
}

/**
 * Clean text for comparison by removing formatting
 */
export function cleanForComparison(text: string): string {
  return text
    .replace(/\r\n/g, '\n') // Normalize line endings
    .replace(/\t/g, '  ') // Replace tabs with spaces
    .replace(/\s+$/gm, '') // Remove trailing whitespace
    .replace(/^\s+/gm, '') // Remove leading whitespace
    .trim();
}
