import { 
  EvaluationContext, 
  EvaluationConfig, 
  EvaluationResult, 
  EvaluationStrategy,
  EvaluationCriteria 
} from './types';
import { CompositeEvaluator } from './evaluators/composite-evaluator';
import { PatternEvaluator } from './evaluators/pattern-evaluator';
import { FileEvaluator } from './evaluators/file-evaluator';
import { CompilationEvaluator } from './evaluators/compilation-evaluator';
import { FunctionalityEvaluator } from './evaluators/functionality-evaluator';
import { createFailureResult, createSuccessResult } from './utils/scoring-utils';
import { logger } from '../utils/logger';

/**
 * Main evaluation service that integrates with the test suite system
 */
export class EvaluationService {
  private cache: Map<string, EvaluationResult>;
  private defaultConfig: EvaluationConfig;

  constructor() {
    this.cache = new Map();
    this.defaultConfig = {
      strategies: [EvaluationStrategy.PATTERN, EvaluationStrategy.FILE],
      weights: {
        [EvaluationStrategy.PATTERN]: 0.6,
        [EvaluationStrategy.FILE]: 0.3,
        [EvaluationStrategy.COMPILATION]: 0.1,
        [EvaluationStrategy.FUNCTIONALITY]: 0.0,
        [EvaluationStrategy.COMPOSITE]: 1.0
      },
      timeout: 30000,
      failFast: false
    };
  }

  /**
   * Evaluate agent output against test case
   */
  async evaluateAgentOutput(
    context: EvaluationContext, 
    config?: Partial<EvaluationConfig>
  ): Promise<EvaluationResult> {
    const startTime = Date.now();
    
    try {
      // Merge configuration
      const evaluationConfig = this.mergeConfig(config);
      
      // Generate cache key
      const cacheKey = this.generateCacheKey(context, evaluationConfig);
      
      // Check cache
      if (this.cache.has(cacheKey)) {
        logger.debug('Returning cached evaluation result');
        return this.cache.get(cacheKey)!;
      }

      // Validate context
      const validationError = this.validateContext(context, evaluationConfig.strategies);
      if (validationError) {
        return createFailureResult(validationError);
      }

      // Determine evaluation strategy
      const strategy = this.determineStrategy(context, evaluationConfig);
      
      // Create evaluator
      const evaluator = this.createEvaluator(strategy, evaluationConfig);
      
      // Perform evaluation
      logger.info(`Starting evaluation with strategy: ${strategy}`);
      const result = await evaluator.evaluate(context);
      
      // Cache result
      this.cache.set(cacheKey, result);
      
      // Log result
      logger.info(`Evaluation completed in ${Date.now() - startTime}ms`, {
        strategy,
        score: result.score,
        success: result.success
      });

      return result;

    } catch (error) {
      const errorMessage = `Evaluation failed: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMessage, { context, config });
      
      return createFailureResult(errorMessage);
    }
  }

  /**
   * Batch evaluate multiple contexts
   */
  async batchEvaluate(
    contexts: EvaluationContext[],
    config?: Partial<EvaluationConfig>
  ): Promise<EvaluationResult[]> {
    const results: EvaluationResult[] = [];
    
    logger.info(`Starting batch evaluation of ${contexts.length} contexts`);
    
    for (let i = 0; i < contexts.length; i++) {
      const context = contexts[i];
      logger.debug(`Evaluating context ${i + 1}/${contexts.length}`);
      
      try {
        const result = await this.evaluateAgentOutput(context, config);
        results.push(result);
      } catch (error) {
        logger.error(`Failed to evaluate context ${i + 1}:`, error);
        results.push(createFailureResult(`Batch evaluation failed for context ${i + 1}`));
      }
    }
    
    logger.info(`Batch evaluation completed. Success rate: ${results.filter(r => r.success).length}/${results.length}`);
    
    return results;
  }

  /**
   * Validate evaluation context
   */
  private validateContext(context: EvaluationContext, strategies: EvaluationStrategy[]): string | null {
    if (!context.testCase) {
      return 'Test case is required';
    }

    // Only require agentOutput when PATTERN strategy is selected
    if (strategies.includes(EvaluationStrategy.PATTERN) && !context.agentOutput) {
      return 'Agent output is required for pattern evaluation';
    }

    if (!context.workspacePath) {
      return 'Workspace path is required';
    }

    return null;
  }

  /**
   * Merge configuration with defaults
   */
  private mergeConfig(config?: Partial<EvaluationConfig>): EvaluationConfig {
    if (!config) {
      return { ...this.defaultConfig };
    }

    return {
      ...this.defaultConfig,
      ...config,
      weights: {
        ...this.defaultConfig.weights,
        ...(config.weights ?? {})
      },
      strategyConfig: {
        ...this.defaultConfig.strategyConfig,
        ...(config.strategyConfig ?? {})
      }
    };
  }

  /**
   * Determine the best evaluation strategy for the context
   */
  private determineStrategy(context: EvaluationContext, config: EvaluationConfig): EvaluationStrategy {
    // If multiple strategies are configured, use composite
    if (config.strategies.length > 1) {
      return EvaluationStrategy.COMPOSITE;
    }
    
    // Use the single configured strategy
    if (config.strategies.length === 1) {
      return config.strategies[0];
    }
    
    // Auto-determine based on test case
    const testCase = context.testCase;
    
    // Check if test case has expected answers (pattern matching)
    if (testCase.expectedAnswers || testCase.answers || testCase.expected) {
      return EvaluationStrategy.PATTERN;
    }
    
    // Check if test case specifies files (file evaluation)
    if (testCase.files || testCase.requiredFiles || testCase.expectedFiles) {
      return EvaluationStrategy.FILE;
    }
    
    // Default to pattern matching
    return EvaluationStrategy.PATTERN;
  }

  /**
   * Create evaluator based on strategy
   */
  private createEvaluator(strategy: EvaluationStrategy, config: EvaluationConfig): any {
    switch (strategy) {
      case EvaluationStrategy.PATTERN:
        return new PatternEvaluator(config.strategyConfig?.[EvaluationStrategy.PATTERN]);
        
      case EvaluationStrategy.FILE:
        return new FileEvaluator(config.strategyConfig?.[EvaluationStrategy.FILE]);
        
      case EvaluationStrategy.COMPILATION:
        return new CompilationEvaluator(config.strategyConfig?.[EvaluationStrategy.COMPILATION]);
        
      case EvaluationStrategy.FUNCTIONALITY:
        return new FunctionalityEvaluator(config.strategyConfig?.[EvaluationStrategy.FUNCTIONALITY]);
        
      case EvaluationStrategy.COMPOSITE:
        return new CompositeEvaluator(config);
        
      default:
        throw new Error(`Unsupported evaluation strategy: ${strategy}`);
    }
  }

  /**
   * Generate cache key for evaluation result
   */
  private generateCacheKey(context: EvaluationContext, config: EvaluationConfig): string {
    const contextHash = this.hashObject({
      testCase: context.testCase,
      agentOutput: context.agentOutput,
      workspacePath: context.workspacePath
    });
    
    const configHash = this.hashObject(config);
    
    return `${contextHash}-${configHash}`;
  }

  /**
   * Simple object hashing for cache keys
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(36);
  }

  /**
   * Clear evaluation cache
   */
  clearCache(): void {
    this.cache.clear();
    logger.debug('Evaluation cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate?: number } {
    return {
      size: this.cache.size
    };
  }

  /**
   * Update default configuration
   */
  updateDefaultConfig(config: Partial<EvaluationConfig>): void {
    this.defaultConfig = {
      ...this.defaultConfig,
      ...config,
      weights: {
        ...this.defaultConfig.weights,
        ...config.weights
      },
      strategyConfig: {
        ...this.defaultConfig.strategyConfig,
        ...config.strategyConfig
      }
    };
    
    logger.info('Default evaluation configuration updated');
  }

  /**
   * Validate evaluation criteria
   */
  validateCriteria(result: EvaluationResult, criteria: EvaluationCriteria): boolean {
    // Check minimum score
    if (criteria.minScore !== undefined && result.score < criteria.minScore) {
      return false;
    }
    
    // Check required strategies (for composite results)
    if (criteria.requiredStrategies && result.details?.metrics?.strategyResults) {
      const strategyResults = result.details.metrics.strategyResults;
      
      for (const requiredStrategy of criteria.requiredStrategies) {
        const strategyResult = strategyResults[requiredStrategy];
        if (!strategyResult || !strategyResult.success) {
          return false;
        }
      }
    }
    
    // Check custom success function
    if (criteria.customSuccess) {
      return criteria.customSuccess(result);
    }
    
    return result.success;
  }

  /**
   * Create evaluation context from test suite data
   */
  static createContext(
    testCase: any,
    agentOutput: string,
    workspacePath: string,
    metadata?: Record<string, any>
  ): EvaluationContext {
    return {
      testCase,
      agentOutput,
      workspacePath,
      metadata
    };
  }

  /**
   * Create default configuration for common scenarios
   */
  static createDefaultConfig(scenario: 'pattern' | 'file' | 'compilation' | 'comprehensive'): EvaluationConfig {
    switch (scenario) {
      case 'pattern':
        return {
          strategies: [EvaluationStrategy.PATTERN],
          weights: { [EvaluationStrategy.PATTERN]: 1.0 } as any,
          timeout: 10000
        };
        
      case 'file':
        return {
          strategies: [EvaluationStrategy.FILE],
          weights: { [EvaluationStrategy.FILE]: 1.0 } as any,
          timeout: 15000
        };
        
      case 'compilation':
        return {
          strategies: [EvaluationStrategy.PATTERN, EvaluationStrategy.FILE, EvaluationStrategy.COMPILATION],
          weights: {
            [EvaluationStrategy.PATTERN]: 0.4,
            [EvaluationStrategy.FILE]: 0.3,
            [EvaluationStrategy.COMPILATION]: 0.3
          } as any,
          timeout: 30000
        };
        
      case 'comprehensive':
        return {
          strategies: [
            EvaluationStrategy.PATTERN,
            EvaluationStrategy.FILE,
            EvaluationStrategy.COMPILATION,
            EvaluationStrategy.FUNCTIONALITY
          ],
          weights: {
            [EvaluationStrategy.PATTERN]: 0.3,
            [EvaluationStrategy.FILE]: 0.3,
            [EvaluationStrategy.COMPILATION]: 0.2,
            [EvaluationStrategy.FUNCTIONALITY]: 0.2
          } as any,
          timeout: 60000
        };
        
      default:
        throw new Error(`Unknown scenario: ${scenario}`);
    }
  }
}
