import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { Evaluator, EvaluationContext, EvaluationResult, EvaluationStrategy, TestScenario } from '../types';
import { findGeneratedFiles } from '../utils/file-utils';

/**
 * Configuration for functionality evaluation
 */
export interface FunctionalityEvaluatorConfig {
  /** Test scenarios to run */
  scenarios: TestScenario[];
  /** Timeout for each test scenario */
  defaultTimeout: number;
  /** Whether to run tests in sandbox */
  useSandbox: boolean;
  /** Node.js executable path */
  nodePath?: string;
}

/**
 * Basic functionality evaluator for simple runtime testing
 */
export class FunctionalityEvaluator extends Evaluator {
  private config: FunctionalityEvaluatorConfig;

  constructor(config: Partial<FunctionalityEvaluatorConfig> = {}) {
    super(EvaluationStrategy.FUNCTIONALITY);
    this.config = {
      scenarios: [],
      defaultTimeout: 10000,
      useSandbox: true,
      nodePath: 'node',
      ...config
    };
  }

  async evaluate(context: EvaluationContext): Promise<EvaluationResult> {
    const startTime = Date.now();
    
    try {
      const { testCase, workspacePath } = context;
      
      if (!existsSync(workspacePath)) {
        return {
          score: 0,
          success: false,
          error: `Workspace path does not exist: ${workspacePath}`,
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Extract test scenarios from test case or use configured ones
      const scenarios = this.extractTestScenarios(testCase);
      
      if (scenarios.length === 0) {
        // Create basic scenarios based on file types
        const generatedScenarios = await this.generateBasicScenarios(workspacePath);
        scenarios.push(...generatedScenarios);
      }

      if (scenarios.length === 0) {
        return {
          score: 0.5, // Neutral score if no tests can be run
          success: true,
          details: { reason: 'No test scenarios available' },
          strategy: this.strategy,
          executionTime: Date.now() - startTime
        };
      }

      // Run test scenarios
      const scenarioResults = await this.runScenarios(workspacePath, scenarios);
      
      // Calculate score
      const score = this.calculateFunctionalityScore(scenarioResults);
      
      return {
        score,
        success: score > 0.6, // Consider successful if most tests pass
        details: {
          scenarioResults,
          totalScenarios: scenarios.length,
          passedScenarios: scenarioResults.filter(r => r.passed).length
        },
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        score: 0,
        success: false,
        error: `Functionality evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
        strategy: this.strategy,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Extract test scenarios from test case
   */
  private extractTestScenarios(testCase: any): TestScenario[] {
    const scenarios: TestScenario[] = [];

    if (testCase.testScenarios) {
      scenarios.push(...testCase.testScenarios);
    }

    if (testCase.functionalTests) {
      scenarios.push(...testCase.functionalTests);
    }

    // Add configured scenarios
    scenarios.push(...this.config.scenarios);

    return scenarios;
  }

  /**
   * Generate basic test scenarios based on workspace content
   */
  private async generateBasicScenarios(workspacePath: string): Promise<TestScenario[]> {
    const scenarios: TestScenario[] = [];
    const files = await findGeneratedFiles(workspacePath, ['.ts', '.js', '.tsx', '.jsx']);

    for (const file of files) {
      const filePath = join(workspacePath, file);
      const content = readFileSync(filePath, 'utf8');

      // Generate scenarios based on file content
      if (content.includes('React') || content.includes('jsx')) {
        scenarios.push(this.createReactComponentScenario(file));
      }

      if (content.includes('express') || content.includes('app.listen')) {
        scenarios.push(this.createExpressServerScenario(file));
      }

      if (content.includes('export') && content.includes('function')) {
        scenarios.push(this.createFunctionExportScenario(file));
      }

      // Basic syntax check scenario
      scenarios.push(this.createSyntaxCheckScenario(file));
    }

    return scenarios;
  }

  /**
   * Create React component test scenario
   */
  private createReactComponentScenario(file: string): TestScenario {
    return {
      name: `React Component Test - ${file}`,
      test: async (workspace: string) => {
        try {
          const filePath = join(workspace, file);
          const content = readFileSync(filePath, 'utf8');
          
          // Basic checks for React component
          const hasReactImport = content.includes('import') && content.includes('React');
          const hasComponentExport = content.includes('export') && 
            (content.includes('function') || content.includes('const') || content.includes('class'));
          const hasJSX = content.includes('<') && content.includes('>');
          
          return hasReactImport && hasComponentExport && hasJSX;
        } catch (error) {
          return false;
        }
      },
      timeout: 5000,
      required: false
    };
  }

  /**
   * Create Express server test scenario
   */
  private createExpressServerScenario(file: string): TestScenario {
    return {
      name: `Express Server Test - ${file}`,
      test: async (workspace: string) => {
        try {
          const filePath = join(workspace, file);
          const content = readFileSync(filePath, 'utf8');
          
          // Basic checks for Express server
          const hasExpressImport = content.includes('express');
          const hasAppCreation = content.includes('express()');
          const hasRoutes = content.includes('.get(') || content.includes('.post(') || 
                           content.includes('.put(') || content.includes('.delete(');
          const hasListen = content.includes('.listen(');
          
          return hasExpressImport && hasAppCreation && (hasRoutes || hasListen);
        } catch (error) {
          return false;
        }
      },
      timeout: 5000,
      required: false
    };
  }

  /**
   * Create function export test scenario
   */
  private createFunctionExportScenario(file: string): TestScenario {
    return {
      name: `Function Export Test - ${file}`,
      test: async (workspace: string) => {
        try {
          const filePath = join(workspace, file);
          
          // Basic check - file exists and is readable
          return existsSync(filePath);
        } catch (error) {
          return false;
        }
      },
      timeout: 8000,
      required: false
    };
  }

  /**
   * Create syntax check scenario
   */
  private createSyntaxCheckScenario(file: string): TestScenario {
    return {
      name: `Syntax Check - ${file}`,
      test: async (workspace: string) => {
        try {
          const filePath = join(workspace, file);
          
          // Basic syntax check - file exists and is readable
          return existsSync(filePath);
        } catch (error) {
          return false;
        }
      },
      timeout: 3000,
      required: true
    };
  }

  /**
   * Run test scenarios
   */
  private async runScenarios(workspacePath: string, scenarios: TestScenario[]): Promise<any[]> {
    const results = [];

    for (const scenario of scenarios) {
      const result = await this.runScenario(workspacePath, scenario);
      results.push({ scenario: scenario.name, ...result });
    }

    return results;
  }

  /**
   * Run a single test scenario
   */
  private async runScenario(workspacePath: string, scenario: TestScenario): Promise<any> {
    const startTime = Date.now();
    const timeout = scenario.timeout || this.config.defaultTimeout;

    try {
      // Create a promise that resolves with the test result
      const testPromise = scenario.test(workspacePath);
      
      // Create a timeout promise
      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout')), timeout);
      });

      // Race between test and timeout
      const passed = await Promise.race([testPromise, timeoutPromise]);
      
      return {
        passed,
        executionTime: Date.now() - startTime,
        required: scenario.required || false
      };

    } catch (error) {
      return {
        passed: false,
        executionTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error),
        required: scenario.required || false
      };
    }
  }



  /**
   * Calculate functionality score
   */
  private calculateFunctionalityScore(results: any[]): number {
    if (results.length === 0) {
      return 0;
    }

    let totalWeight = 0;
    let weightedScore = 0;

    for (const result of results) {
      const weight = result.required ? 1.0 : 0.5;
      totalWeight += weight;
      
      if (result.passed) {
        weightedScore += weight;
      }
    }

    return totalWeight > 0 ? weightedScore / totalWeight : 0;
  }
}
