{"name": "Express API Basics", "description": "Tests AI coding agents' ability to create basic Express.js APIs with proper routing and middleware", "system_prompt": "You are an experienced Node.js backend developer. Create Express.js routes and middleware using modern JavaScript/TypeScript patterns. Follow REST API conventions, handle errors properly, and use appropriate HTTP status codes. Always validate request data and send proper JSON responses.", "tests": [{"prompt": "Create an Express GET route at '/users' that returns a JSON array of user objects. Each user should have id, name, and email properties. Return a 200 status code.", "answers": ["app.get('/users'", "res.json(", "res.status(200)", "id:", "name:", "email:"], "negative_answers": ["app.post('/users'", "res.send(", "res.status(404)", "res.redirect"]}, {"prompt": "Create an Express POST route at '/users' that accepts user registration data from req.body (name and email), validates that both fields are present, and returns the created user with a generated id. Return 400 status for missing fields.", "answers": ["app.post('/users'", "req.body", "req.body.name", "req.body.email", "res.status(400)", "res.json("], "negative_answers": ["app.get('/users'", "req.params", "res.status(200)", "res.send("]}, {"prompt": "Create an Express middleware function called 'authMiddleware' that checks for an 'Authorization' header in the request. If the header is missing, return a 401 status with an error message. If present, call next() to continue.", "answers": ["function authMiddleware", "req.headers.authorization", "res.status(401)", "next()", "return"], "negative_answers": ["res.status(200)", "res.status(404)", "req.body.authorization", "res.redirect"]}]}