# Test Suite Benchmarking

This document describes the test suite benchmarking system in ts-bench, which provides prompt-based evaluation compatible with skatebench's frontend visualization.

## Overview

ts-bench supports two execution modes:

1. **Exercise Mode** (default): File-based exercises using Exercism-style problems
2. **Test Suite Mode**: Prompt-based test cases for direct model evaluation

The test suite mode is designed to be compatible with skatebench's output format, enabling seamless integration with existing visualization tools.

## Test Suite vs Exercise Mode

| Feature | Exercise Mode | Test Suite Mode |
|---------|---------------|-----------------|
| Input Format | File-based exercises with instructions.md | JSON test suites with prompts |
| Execution | Agent modifies files, tests run against code | Agent responds to prompts, evaluated against expected answers |
| Output Format | Exercise-focused results | Model-focused rankings (skatebench compatible) |
| Use Case | Code generation and modification tasks | Direct prompt evaluation and model comparison |

## CLI Usage

### Basic Test Suite Execution

```bash
# Run all test suites
ts-bench --mode test-suite --agent claude --model claude-3-sonnet

# Run specific test suite
ts-bench --mode test-suite --test-suite math-problems --agent claude --model claude-3-sonnet

# Run multiple test suites
ts-bench --mode test-suite --test-suite-list math-problems,logic-puzzles --agent claude --model claude-3-sonnet

# List available test suites
ts-bench --mode test-suite --list-test-suites
```

### Output Formats

```bash
# Standard ts-bench output
ts-bench --mode test-suite --test-suite math-problems --agent claude --model claude-3-sonnet

# Skatebench-compatible output
ts-bench --mode test-suite --test-suite math-problems --agent claude --model claude-3-sonnet --skatebench-output

# Save results to file
ts-bench --mode test-suite --test-suite math-problems --agent claude --model claude-3-sonnet --output-dir ./results

# Generate leaderboard
ts-bench --mode test-suite --test-suite math-problems --agent claude --model claude-3-sonnet --generate-leaderboard
```

### Advanced Options

```bash
# Custom test suite directory
ts-bench --mode test-suite --test-suite-path ./custom-test-suites --agent claude --model claude-3-sonnet

# Concurrency control
ts-bench --mode test-suite --test-suite math-problems --max-concurrency 3 --agent claude --model claude-3-sonnet

# Timeout configuration
ts-bench --mode test-suite --test-suite math-problems --timeout 30 --agent claude --model claude-3-sonnet
```

## Skatebench Output Format

The skatebench-compatible output includes:

### Rankings Structure
```json
{
  "metadata": {
    "generatedAt": "2024-01-15T10:30:00.000Z",
    "totalTestCases": 100,
    "totalModels": 3,
    "benchmarkType": "test-suite",
    "version": "1.0.0"
  },
  "rankings": [
    {
      "rank": 1,
      "model": "claude-3-sonnet",
      "agent": "claude",
      "provider": "anthropic",
      "metrics": {
        "successRate": 85.5,
        "errorRate": 2.1,
        "averageDuration": 1250.5,
        "totalCost": 0.0234,
        "averageCostPerTest": 0.000234
      },
      "counts": {
        "total": 100,
        "correct": 85,
        "incorrect": 13,
        "errors": 2
      }
    }
  ],
  "summary": {
    "totalTests": 100,
    "totalCorrect": 85,
    "totalIncorrect": 13,
    "totalErrors": 2,
    "overallSuccessRate": 85.0,
    "overallErrorRate": 2.0,
    "totalCost": 0.0234,
    "averageDuration": 1250.5
  }
}
```

## Creating Test Suites

Test suites are JSON files with the following structure:

```json
{
  "id": "math-problems",
  "name": "Basic Math Problems",
  "description": "Simple arithmetic and algebra problems",
  "version": "1.0.0",
  "systemPrompt": "You are a helpful assistant that solves math problems.",
  "testCases": [
    {
      "id": "addition-1",
      "prompt": "What is 15 + 27?",
      "expectedAnswer": "42",
      "metadata": {
        "difficulty": "easy",
        "category": "arithmetic"
      }
    },
    {
      "id": "algebra-1",
      "prompt": "Solve for x: 2x + 5 = 13",
      "expectedAnswer": "x = 4",
      "metadata": {
        "difficulty": "medium",
        "category": "algebra"
      }
    }
  ]
}
```

### Test Suite Directory Structure

```
test-suites/
├── math-problems/
│   └── suite.json
├── logic-puzzles/
│   └── suite.json
└── coding-challenges/
    └── suite.json
```

## Cost Estimation

The system includes built-in cost estimation for common models:

### Supported Models
- **OpenAI**: GPT-4, GPT-4 Turbo, GPT-4o, GPT-3.5 Turbo
- **Anthropic**: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku, Claude 3.5 Sonnet
- **Google**: Gemini Pro, Gemini 1.5 Pro

### Cost Calculation
Costs are estimated based on:
- Input token count (prompt length)
- Output token count (response length)
- Model-specific pricing (per 1000 tokens)

## Migration Guide

### From Exercise-based to Test Suite-based

1. **Convert Exercise Data**:
   ```typescript
   // Old exercise format
   const exercise = {
     name: "two-fer",
     instructions: "Create a function that returns...",
     files: ["two-fer.ts", "two-fer.test.ts"]
   };
   
   // New test suite format
   const testCase = {
     id: "two-fer-1",
     prompt: "Create a TypeScript function that returns...",
     expectedAnswer: "function twoFer(name?: string): string { ... }"
   };
   ```

2. **Update CLI Commands**:
   ```bash
   # Old exercise mode
   ts-bench --exercise two-fer --agent claude --model claude-3-sonnet
   
   # New test suite mode
   ts-bench --mode test-suite --test-suite coding-challenges --agent claude --model claude-3-sonnet
   ```

3. **Adapt Output Processing**:
   ```typescript
   // Old exercise results
   interface TestResult {
     exercise: string;
     agentSuccess: boolean;
     testSuccess: boolean;
   }
   
   // New test suite results
   interface TestSuiteResult {
     suiteId: string;
     testCaseId: string;
     correct: boolean;
     successRate: number;
   }
   ```

## Performance Considerations

### Concurrency
- Default concurrency: 1 (sequential execution)
- Recommended concurrency: 2-5 for most use cases
- Higher concurrency may hit API rate limits

### Memory Usage
- Test suite results are kept in memory during execution
- Large test suites (>1000 test cases) may require memory optimization
- Consider batch processing for very large datasets

### API Rate Limits
- Monitor API usage to avoid rate limiting
- Implement exponential backoff for failed requests
- Consider using multiple API keys for higher throughput

## Best Practices

### Test Suite Design
1. **Clear Prompts**: Write unambiguous, specific prompts
2. **Consistent Format**: Use consistent answer formats within a suite
3. **Balanced Difficulty**: Include a mix of easy, medium, and hard problems
4. **Metadata**: Include relevant metadata for analysis

### Evaluation Strategy
1. **Multiple Runs**: Run multiple iterations for statistical significance
2. **Cross-validation**: Test across different model configurations
3. **Error Analysis**: Analyze failure patterns to improve test suites
4. **Cost Monitoring**: Track costs to optimize model selection

### Integration with Frontend
1. **Skatebench Compatibility**: Use `--skatebench-output` for frontend integration
2. **Regular Updates**: Update leaderboards regularly with new results
3. **Version Control**: Track test suite versions for reproducible results
4. **Documentation**: Document test suite changes and model updates

## Troubleshooting

### Common Issues

1. **Test Suite Not Found**
   ```
   Error: Test suite 'math-problems' not found
   ```
   - Check test suite path with `--test-suite-path`
   - Verify suite.json file exists and is valid JSON

2. **API Rate Limiting**
   ```
   Error: Rate limit exceeded
   ```
   - Reduce concurrency with `--max-concurrency 1`
   - Add delays between requests
   - Check API key quotas

3. **Invalid Skatebench Output**
   ```
   Error: Invalid skatebench format
   ```
   - Ensure all required fields are present
   - Validate JSON structure with `validateSkatebenchOutput()`

4. **High Costs**
   ```
   Warning: Estimated cost exceeds budget
   ```
   - Use smaller models for testing
   - Reduce test suite size
   - Monitor token usage

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
ts-bench --mode test-suite --test-suite math-problems --verbose --agent claude --model claude-3-sonnet
```

This provides detailed information about:
- Test case execution
- API requests and responses
- Cost calculations
- Error details

## Future Enhancements

Planned improvements include:
- Streaming evaluation for real-time feedback
- Advanced cost optimization strategies
- Integration with more model providers
- Enhanced statistical analysis
- Automated test suite generation
- Performance benchmarking tools
